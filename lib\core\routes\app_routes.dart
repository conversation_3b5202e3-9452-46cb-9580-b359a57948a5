import 'package:flutter/material.dart';
import '../../features/admin_compagnie/presentation/pages/admin_compagnie_dashboard.dart';
import '../../features/expert/presentation/pages/expert_dashboard.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/change_password_page.dart';

/// 🗺️ Configuration des routes de l'application
/// 
/// Gère toutes les routes et redirections selon les rôles utilisateur
class AppRoutes {
  static const String login = '/login';
  static const String changePassword = '/change-password';
  static const String superAdminDashboard = '/super-admin-dashboard';
  static const String compagnieDashboard = '/compagnie-dashboard';
  static const String agenceDashboard = '/agence-dashboard';
  static const String agentDashboard = '/agent-dashboard';
  static const String expertDashboard = '/expert-dashboard';
  static const String conducteurDashboard = '/conducteur-dashboard';

  /// 📋 Générer les routes de l'application
  static Map<String, WidgetBuilder> generateRoutes() {
    return {
      login: (context) => const LoginPage(),
      changePassword: (context) => const ChangePasswordPage(),
      superAdminDashboard: (context) => _buildSuperAdminDashboard(),
      expertDashboard: (context) => const ExpertDashboard(),
    };
  }

  /// 🔧 Construire le dashboard Super Admin (placeholder)
  static Widget _buildSuperAdminDashboard() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Super Admin Dashboard'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.admin_panel_settings, size: 64, color: Colors.purple),
            SizedBox(height: 16),
            Text(
              'Super Admin Dashboard',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 32),
            Text(
              'Interface en développement',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 🔄 Générateur de routes dynamiques
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    final uri = Uri.parse(settings.name ?? '');
    
    // Route dynamique pour dashboard compagnie
    if (uri.pathSegments.isNotEmpty && uri.pathSegments[0] == 'compagnie-dashboard') {
      if (uri.pathSegments.length >= 2) {
        final compagnieId = uri.pathSegments[1];
        return MaterialPageRoute(
          builder: (context) => AdminCompagnieDashboard(compagnieId: compagnieId),
          settings: settings,
        );
      }
    }

    // Route dynamique pour dashboard agence
    if (uri.pathSegments.isNotEmpty && uri.pathSegments[0] == 'agence-dashboard') {
      if (uri.pathSegments.length >= 2) {
        final agenceId = uri.pathSegments[1];
        return MaterialPageRoute(
          builder: (context) => _buildAgenceDashboard(agenceId),
          settings: settings,
        );
      }
    }

    // Route dynamique pour dashboard agent
    if (uri.pathSegments.isNotEmpty && uri.pathSegments[0] == 'agent-dashboard') {
      if (uri.pathSegments.length >= 2) {
        final agenceId = uri.pathSegments[1];
        return MaterialPageRoute(
          builder: (context) => _buildAgentDashboard(agenceId),
          settings: settings,
        );
      }
    }

    // Route pour dashboard conducteur
    if (settings.name == conducteurDashboard) {
      return MaterialPageRoute(
        builder: (context) => _buildConducteurDashboard(),
        settings: settings,
      );
    }

    // Route par défaut
    return MaterialPageRoute(
      builder: (context) => const LoginPage(),
      settings: settings,
    );
  }

  /// 🏪 Construire le dashboard agence (placeholder)
  static Widget _buildAgenceDashboard(String agenceId) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Agence'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.store, size: 64, color: Colors.blue),
            const SizedBox(height: 16),
            const Text(
              'Dashboard Admin Agence',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Agence ID: $agenceId',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 32),
            const Text(
              'Interface en développement',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 👨‍💼 Construire le dashboard agent (placeholder)
  static Widget _buildAgentDashboard(String agenceId) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Agent'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.person_pin, size: 64, color: Colors.green),
            const SizedBox(height: 16),
            const Text(
              'Dashboard Agent',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'Agence ID: $agenceId',
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 32),
            const Text(
              'Interface en développement',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 🚗 Construire le dashboard conducteur (placeholder)
  static Widget _buildConducteurDashboard() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Conducteur'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.directions_car, size: 64, color: Colors.orange),
            SizedBox(height: 16),
            Text(
              'Dashboard Conducteur',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 32),
            Text(
              'Interface en développement',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  /// 🔐 Vérifier si une route nécessite une authentification
  static bool requiresAuth(String route) {
    const publicRoutes = [
      login,
    ];
    
    return !publicRoutes.contains(route);
  }

  /// 🏷️ Obtenir le rôle requis pour une route
  static String? getRequiredRole(String route) {
    if (route.startsWith('/super-admin')) {
      return 'super_admin';
    } else if (route.startsWith('/compagnie-dashboard')) {
      return 'admin_compagnie';
    } else if (route.startsWith('/agence-dashboard')) {
      return 'admin_agence';
    } else if (route.startsWith('/agent-dashboard')) {
      return 'agent_agence';
    } else if (route.startsWith('/expert-dashboard')) {
      return 'expert_auto';
    } else if (route.startsWith('/conducteur-dashboard')) {
      return 'conducteur';
    }
    
    return null;
  }

  /// 🎯 Obtenir la route par défaut pour un rôle
  static String getDefaultRouteForRole(String role, {String? compagnieId, String? agenceId}) {
    switch (role) {
      case 'super_admin':
        return superAdminDashboard;
      case 'admin_compagnie':
        return compagnieId != null ? '/compagnie-dashboard/$compagnieId' : compagnieDashboard;
      case 'admin_agence':
        return agenceId != null ? '/agence-dashboard/$agenceId' : agenceDashboard;
      case 'agent_agence':
        return agenceId != null ? '/agent-dashboard/$agenceId' : agentDashboard;
      case 'expert_auto':
        return expertDashboard;
      case 'conducteur':
        return conducteurDashboard;
      default:
        return login;
    }
  }

  /// 📱 Obtenir le titre de la page pour une route
  static String getPageTitle(String route) {
    if (route.startsWith('/super-admin')) {
      return 'Super Admin Dashboard';
    } else if (route.startsWith('/compagnie-dashboard')) {
      return 'Admin Compagnie Dashboard';
    } else if (route.startsWith('/agence-dashboard')) {
      return 'Admin Agence Dashboard';
    } else if (route.startsWith('/agent-dashboard')) {
      return 'Agent Dashboard';
    } else if (route.startsWith('/expert-dashboard')) {
      return 'Expert Dashboard';
    } else if (route.startsWith('/conducteur-dashboard')) {
      return 'Conducteur Dashboard';
    } else if (route == login) {
      return 'Connexion';
    } else if (route == changePassword) {
      return 'Changer le mot de passe';
    }
    
    return 'Constat Tunisie';
  }

  /// 🎨 Obtenir l'icône pour une route
  static IconData getPageIcon(String route) {
    if (route.startsWith('/super-admin')) {
      return Icons.admin_panel_settings;
    } else if (route.startsWith('/compagnie-dashboard')) {
      return Icons.business;
    } else if (route.startsWith('/agence-dashboard')) {
      return Icons.store;
    } else if (route.startsWith('/agent-dashboard')) {
      return Icons.person_pin;
    } else if (route.startsWith('/expert-dashboard')) {
      return Icons.engineering;
    } else if (route.startsWith('/conducteur-dashboard')) {
      return Icons.directions_car;
    }
    
    return Icons.home;
  }
}
