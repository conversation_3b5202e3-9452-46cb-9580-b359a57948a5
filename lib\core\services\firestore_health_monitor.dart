import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'robust_firestore_service.dart';

/// 🏥 Moniteur de santé Firestore
/// 
/// Ce service surveille la santé de Firestore et fournit des informations
/// sur la connectivité et les performances
class FirestoreHealthMonitor {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  /// 🔍 Effectuer un diagnostic complet de Firestore
  static Future<Map<String, dynamic>> performFullDiagnostic() async {
    debugPrint('[FIRESTORE_HEALTH] 🔍 Début diagnostic complet...');
    
    final diagnosticResults = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'overall_status': 'unknown',
      'recommendations': <String>[],
    };

    // Test 1: Connectivité de base
    try {
      debugPrint('[FIRESTORE_HEALTH] 📡 Test connectivité de base...');
      final startTime = DateTime.now();
      
      await _firestore.enableNetwork();
      
      final endTime = DateTime.now();
      final latency = endTime.difference(startTime).inMilliseconds;
      
      diagnosticResults['tests']['connectivity'] = {
        'status': 'success',
        'latency_ms': latency,
        'message': 'Connectivité Firestore OK',
      };
      
      debugPrint('[FIRESTORE_HEALTH] ✅ Connectivité OK (${latency}ms)');
      
    } catch (e) {
      diagnosticResults['tests']['connectivity'] = {
        'status': 'failed',
        'error': e.toString(),
        'message': 'Problème de connectivité Firestore',
      };
      
      debugPrint('[FIRESTORE_HEALTH] ❌ Connectivité échouée: $e');
    }

    // Test 2: Lecture simple
    try {
      debugPrint('[FIRESTORE_HEALTH] 📖 Test lecture simple...');
      final readResult = await RobustFirestoreService.readDocumentWithRetry(
        collection: 'health_check',
        documentId: 'test_read',
        maxRetries: 2,
      );
      
      diagnosticResults['tests']['read'] = {
        'status': readResult['success'] ? 'success' : 'failed',
        'attempts': readResult['attempts'],
        'message': readResult['success'] 
            ? 'Lecture Firestore fonctionnelle' 
            : 'Problème de lecture Firestore',
      };
      
      if (readResult['success']) {
        debugPrint('[FIRESTORE_HEALTH] ✅ Lecture OK');
      } else {
        debugPrint('[FIRESTORE_HEALTH] ❌ Lecture échouée');
      }
      
    } catch (e) {
      diagnosticResults['tests']['read'] = {
        'status': 'failed',
        'error': e.toString(),
        'message': 'Erreur lors du test de lecture',
      };
    }

    // Test 3: Écriture simple
    try {
      debugPrint('[FIRESTORE_HEALTH] 📝 Test écriture simple...');
      final testData = {
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
        'test_id': DateTime.now().millisecondsSinceEpoch.toString(),
      };
      
      final writeResult = await RobustFirestoreService.writeDocumentWithRetry(
        collection: 'health_check',
        documentId: 'test_write_${DateTime.now().millisecondsSinceEpoch}',
        data: testData,
        maxRetries: 2,
      );
      
      diagnosticResults['tests']['write'] = {
        'status': writeResult['success'] ? 'success' : 'failed',
        'attempts': writeResult['attempts'],
        'message': writeResult['success'] 
            ? 'Écriture Firestore fonctionnelle' 
            : 'Problème d\'écriture Firestore',
      };
      
      if (writeResult['success']) {
        debugPrint('[FIRESTORE_HEALTH] ✅ Écriture OK');
      } else {
        debugPrint('[FIRESTORE_HEALTH] ❌ Écriture échouée');
      }
      
    } catch (e) {
      diagnosticResults['tests']['write'] = {
        'status': 'failed',
        'error': e.toString(),
        'message': 'Erreur lors du test d\'écriture',
      };
    }

    // Test 4: Requête simple
    try {
      debugPrint('[FIRESTORE_HEALTH] 🔍 Test requête simple...');
      final queryResult = await RobustFirestoreService.queryWithRetry(
        collection: 'health_check',
        limit: 1,
        maxRetries: 2,
      );
      
      diagnosticResults['tests']['query'] = {
        'status': queryResult['success'] ? 'success' : 'failed',
        'attempts': queryResult['attempts'],
        'count': queryResult['count'],
        'message': queryResult['success'] 
            ? 'Requêtes Firestore fonctionnelles' 
            : 'Problème de requête Firestore',
      };
      
      if (queryResult['success']) {
        debugPrint('[FIRESTORE_HEALTH] ✅ Requête OK (${queryResult['count']} docs)');
      } else {
        debugPrint('[FIRESTORE_HEALTH] ❌ Requête échouée');
      }
      
    } catch (e) {
      diagnosticResults['tests']['query'] = {
        'status': 'failed',
        'error': e.toString(),
        'message': 'Erreur lors du test de requête',
      };
    }

    // Calculer le statut global
    final tests = diagnosticResults['tests'] as Map<String, dynamic>;
    final successfulTests = tests.values.where((test) => test['status'] == 'success').length;
    final totalTests = tests.length;
    
    if (successfulTests == totalTests) {
      diagnosticResults['overall_status'] = 'healthy';
      diagnosticResults['recommendations'].add('✅ Firestore fonctionne parfaitement');
    } else if (successfulTests >= totalTests / 2) {
      diagnosticResults['overall_status'] = 'degraded';
      diagnosticResults['recommendations'].add('⚠️ Firestore fonctionne partiellement - Vérifier la connectivité');
    } else {
      diagnosticResults['overall_status'] = 'unhealthy';
      diagnosticResults['recommendations'].add('❌ Problèmes majeurs avec Firestore - Vérifier la configuration');
    }

    // Recommandations spécifiques
    if (tests['connectivity']?['status'] != 'success') {
      diagnosticResults['recommendations'].add('🔧 Vérifier la connexion Internet');
    }
    
    if (tests['write']?['status'] != 'success') {
      diagnosticResults['recommendations'].add('🔧 Vérifier les règles de sécurité Firestore');
    }

    debugPrint('[FIRESTORE_HEALTH] 📊 Diagnostic terminé: ${diagnosticResults['overall_status']}');
    debugPrint('[FIRESTORE_HEALTH] 📈 Tests réussis: $successfulTests/$totalTests');
    
    return diagnosticResults;
  }

  /// ⚡ Test rapide de santé Firestore
  static Future<bool> quickHealthCheck() async {
    try {
      debugPrint('[FIRESTORE_HEALTH] ⚡ Test rapide de santé...');
      
      final result = await RobustFirestoreService.readDocumentWithRetry(
        collection: 'health_check',
        documentId: 'quick_test',
        maxRetries: 1,
        initialDelay: const Duration(milliseconds: 200),
      );
      
      final isHealthy = result['success'] == true;
      debugPrint('[FIRESTORE_HEALTH] ${isHealthy ? "✅" : "❌"} Test rapide: ${isHealthy ? "OK" : "KO"}');
      
      return isHealthy;
      
    } catch (e) {
      debugPrint('[FIRESTORE_HEALTH] ❌ Test rapide échoué: $e');
      return false;
    }
  }

  /// 📊 Obtenir les métriques de performance
  static Future<Map<String, dynamic>> getPerformanceMetrics() async {
    final metrics = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'read_latency_ms': null,
      'write_latency_ms': null,
      'query_latency_ms': null,
    };

    // Test latence lecture
    try {
      final startTime = DateTime.now();
      await _firestore.collection('health_check').doc('perf_test').get();
      final endTime = DateTime.now();
      metrics['read_latency_ms'] = endTime.difference(startTime).inMilliseconds;
    } catch (e) {
      metrics['read_error'] = e.toString();
    }

    // Test latence écriture
    try {
      final startTime = DateTime.now();
      await _firestore.collection('health_check').doc('perf_test').set({
        'timestamp': FieldValue.serverTimestamp(),
        'test': 'performance',
      });
      final endTime = DateTime.now();
      metrics['write_latency_ms'] = endTime.difference(startTime).inMilliseconds;
    } catch (e) {
      metrics['write_error'] = e.toString();
    }

    // Test latence requête
    try {
      final startTime = DateTime.now();
      await _firestore.collection('health_check').limit(1).get();
      final endTime = DateTime.now();
      metrics['query_latency_ms'] = endTime.difference(startTime).inMilliseconds;
    } catch (e) {
      metrics['query_error'] = e.toString();
    }

    return metrics;
  }

  /// 🔧 Nettoyer les documents de test
  static Future<void> cleanupTestDocuments() async {
    try {
      debugPrint('[FIRESTORE_HEALTH] 🧹 Nettoyage des documents de test...');
      
      final snapshot = await _firestore
          .collection('health_check')
          .where('test', isEqualTo: true)
          .get();
      
      final batch = _firestore.batch();
      for (final doc in snapshot.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      
      debugPrint('[FIRESTORE_HEALTH] ✅ ${snapshot.docs.length} documents de test supprimés');
      
    } catch (e) {
      debugPrint('[FIRESTORE_HEALTH] ❌ Erreur nettoyage: $e');
    }
  }

  /// 📱 Afficher un rapport de santé formaté
  static String formatHealthReport(Map<String, dynamic> diagnostic) {
    final buffer = StringBuffer();
    
    buffer.writeln('🏥 RAPPORT DE SANTÉ FIRESTORE');
    buffer.writeln('=' * 40);
    buffer.writeln('📅 Timestamp: ${diagnostic['timestamp']}');
    buffer.writeln('🎯 Statut global: ${diagnostic['overall_status']}');
    buffer.writeln();
    
    buffer.writeln('📊 TESTS DÉTAILLÉS:');
    final tests = diagnostic['tests'] as Map<String, dynamic>;
    tests.forEach((testName, result) {
      final status = result['status'] == 'success' ? '✅' : '❌';
      buffer.writeln('  $status $testName: ${result['message']}');
      if (result['attempts'] != null) {
        buffer.writeln('    Tentatives: ${result['attempts']}');
      }
      if (result['latency_ms'] != null) {
        buffer.writeln('    Latence: ${result['latency_ms']}ms');
      }
    });
    
    buffer.writeln();
    buffer.writeln('💡 RECOMMANDATIONS:');
    final recommendations = diagnostic['recommendations'] as List<dynamic>;
    for (final rec in recommendations) {
      buffer.writeln('  • $rec');
    }
    
    return buffer.toString();
  }
}
