import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 🛡️ Service Firestore robuste avec retry automatique
/// 
/// Ce service gère automatiquement les interruptions réseau et les erreurs
/// temporaires de Firestore avec un système de retry intelligent
class RobustFirestoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 📝 Écrire un document avec retry automatique
  static Future<Map<String, dynamic>> writeDocumentWithRetry({
    required String collection,
    required String documentId,
    required Map<String, dynamic> data,
    int maxRetries = 5,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        debugPrint('[ROBUST_FIRESTORE] 📝 Tentative ${attempt + 1}/$maxRetries - Écriture: $collection/$documentId');

        await _firestore
            .collection(collection)
            .doc(documentId)
            .set(data);

        debugPrint('[ROBUST_FIRESTORE] ✅ Écriture réussie après ${attempt + 1} tentative(s)');
        
        return {
          'success': true,
          'attempts': attempt + 1,
          'documentId': documentId,
          'message': 'Document créé avec succès',
        };

      } catch (e) {
        attempt++;
        
        debugPrint('[ROBUST_FIRESTORE] ❌ Tentative $attempt échouée: $e');

        // Vérifier si c'est une erreur temporaire
        if (_isTemporaryError(e) && attempt < maxRetries) {
          debugPrint('[ROBUST_FIRESTORE] ⏳ Attente ${currentDelay.inSeconds}s avant retry...');
          
          await Future.delayed(currentDelay);
          
          // Backoff exponentiel avec jitter
          currentDelay = Duration(
            milliseconds: (currentDelay.inMilliseconds * 1.5).round() + 
                         (DateTime.now().millisecondsSinceEpoch % 1000),
          );
          
          continue;
        }

        // Erreur définitive ou max retries atteint
        debugPrint('[ROBUST_FIRESTORE] 💥 Échec définitif après $attempt tentatives');
        
        return {
          'success': false,
          'attempts': attempt,
          'error': e.toString(),
          'message': 'Impossible d\'écrire le document après $attempt tentatives',
        };
      }
    }

    return {
      'success': false,
      'attempts': maxRetries,
      'error': 'Max retries reached',
      'message': 'Nombre maximum de tentatives atteint',
    };
  }

  /// 📖 Lire un document avec retry automatique
  static Future<Map<String, dynamic>> readDocumentWithRetry({
    required String collection,
    required String documentId,
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        debugPrint('[ROBUST_FIRESTORE] 📖 Tentative ${attempt + 1}/$maxRetries - Lecture: $collection/$documentId');

        final doc = await _firestore
            .collection(collection)
            .doc(documentId)
            .get();

        debugPrint('[ROBUST_FIRESTORE] ✅ Lecture réussie après ${attempt + 1} tentative(s)');
        
        return {
          'success': true,
          'exists': doc.exists,
          'data': doc.exists ? doc.data() : null,
          'attempts': attempt + 1,
        };

      } catch (e) {
        attempt++;
        
        debugPrint('[ROBUST_FIRESTORE] ❌ Tentative lecture $attempt échouée: $e');

        if (_isTemporaryError(e) && attempt < maxRetries) {
          debugPrint('[ROBUST_FIRESTORE] ⏳ Attente ${currentDelay.inSeconds}s avant retry lecture...');
          
          await Future.delayed(currentDelay);
          currentDelay = Duration(milliseconds: (currentDelay.inMilliseconds * 1.2).round());
          continue;
        }

        return {
          'success': false,
          'attempts': attempt,
          'error': e.toString(),
        };
      }
    }

    return {
      'success': false,
      'attempts': maxRetries,
      'error': 'Max retries reached for read operation',
    };
  }

  /// 🔍 Exécuter une requête avec retry automatique
  static Future<Map<String, dynamic>> queryWithRetry({
    required String collection,
    Map<String, dynamic>? where,
    String? orderBy,
    bool descending = false,
    int? limit,
    int maxRetries = 3,
  }) async {
    int attempt = 0;
    Duration currentDelay = const Duration(milliseconds: 500);

    while (attempt < maxRetries) {
      try {
        debugPrint('[ROBUST_FIRESTORE] 🔍 Tentative ${attempt + 1}/$maxRetries - Requête: $collection');

        Query<Map<String, dynamic>> query = _firestore.collection(collection);

        // Appliquer les filtres
        if (where != null) {
          where.forEach((field, value) {
            query = query.where(field, isEqualTo: value);
          });
        }

        if (orderBy != null) {
          query = query.orderBy(orderBy, descending: descending);
        }

        if (limit != null) {
          query = query.limit(limit);
        }

        final snapshot = await query.get();

        debugPrint('[ROBUST_FIRESTORE] ✅ Requête réussie: ${snapshot.docs.length} documents');
        
        return {
          'success': true,
          'documents': snapshot.docs.map((doc) => {
            'id': doc.id,
            ...doc.data(),
          }).toList(),
          'count': snapshot.docs.length,
          'attempts': attempt + 1,
        };

      } catch (e) {
        attempt++;
        
        debugPrint('[ROBUST_FIRESTORE] ❌ Tentative requête $attempt échouée: $e');

        if (_isTemporaryError(e) && attempt < maxRetries) {
          await Future.delayed(currentDelay);
          currentDelay = Duration(milliseconds: (currentDelay.inMilliseconds * 1.2).round());
          continue;
        }

        return {
          'success': false,
          'attempts': attempt,
          'error': e.toString(),
          'documents': <Map<String, dynamic>>[],
          'count': 0,
        };
      }
    }

    return {
      'success': false,
      'attempts': maxRetries,
      'error': 'Max retries reached for query',
      'documents': <Map<String, dynamic>>[],
      'count': 0,
    };
  }

  /// 🔄 Mettre à jour un document avec retry
  static Future<Map<String, dynamic>> updateDocumentWithRetry({
    required String collection,
    required String documentId,
    required Map<String, dynamic> data,
    int maxRetries = 3,
  }) async {
    int attempt = 0;
    Duration currentDelay = const Duration(milliseconds: 500);

    while (attempt < maxRetries) {
      try {
        debugPrint('[ROBUST_FIRESTORE] 🔄 Tentative ${attempt + 1}/$maxRetries - Mise à jour: $collection/$documentId');

        await _firestore
            .collection(collection)
            .doc(documentId)
            .update(data);

        debugPrint('[ROBUST_FIRESTORE] ✅ Mise à jour réussie après ${attempt + 1} tentative(s)');
        
        return {
          'success': true,
          'attempts': attempt + 1,
          'message': 'Document mis à jour avec succès',
        };

      } catch (e) {
        attempt++;
        
        if (_isTemporaryError(e) && attempt < maxRetries) {
          await Future.delayed(currentDelay);
          currentDelay = Duration(milliseconds: (currentDelay.inMilliseconds * 1.2).round());
          continue;
        }

        return {
          'success': false,
          'attempts': attempt,
          'error': e.toString(),
        };
      }
    }

    return {
      'success': false,
      'attempts': maxRetries,
      'error': 'Max retries reached for update',
    };
  }

  /// ❓ Vérifier si une erreur est temporaire
  static bool _isTemporaryError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    return errorString.contains('unavailable') ||
           errorString.contains('deadline exceeded') ||
           errorString.contains('timeout') ||
           errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('cancelled') ||
           errorString.contains('aborted');
  }

  /// 📊 Obtenir les statistiques de santé Firestore
  static Future<Map<String, dynamic>> getFirestoreHealth() async {
    try {
      final startTime = DateTime.now();
      
      // Test simple de lecture
      await _firestore
          .collection('health_check')
          .doc('test')
          .get();
      
      final endTime = DateTime.now();
      final latency = endTime.difference(startTime).inMilliseconds;
      
      return {
        'status': 'healthy',
        'latency_ms': latency,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
    } catch (e) {
      return {
        'status': 'unhealthy',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔧 Exécuter une opération avec retry personnalisé
  static Future<T> executeWithRetry<T>({
    required Future<T> Function() operation,
    required String operationName,
    int maxRetries = 3,
    Duration initialDelay = const Duration(milliseconds: 500),
    bool Function(dynamic error)? isRetryableError,
  }) async {
    int attempt = 0;
    Duration currentDelay = initialDelay;

    while (attempt < maxRetries) {
      try {
        debugPrint('[ROBUST_FIRESTORE] 🔧 $operationName - Tentative ${attempt + 1}/$maxRetries');
        
        final result = await operation();
        
        debugPrint('[ROBUST_FIRESTORE] ✅ $operationName réussi après ${attempt + 1} tentative(s)');
        return result;

      } catch (e) {
        attempt++;
        
        final shouldRetry = isRetryableError?.call(e) ?? _isTemporaryError(e);
        
        if (shouldRetry && attempt < maxRetries) {
          debugPrint('[ROBUST_FIRESTORE] ⏳ $operationName - Retry dans ${currentDelay.inSeconds}s...');
          
          await Future.delayed(currentDelay);
          currentDelay = Duration(milliseconds: (currentDelay.inMilliseconds * 1.3).round());
          continue;
        }

        debugPrint('[ROBUST_FIRESTORE] 💥 $operationName échoué définitivement: $e');
        rethrow;
      }
    }

    throw Exception('$operationName: Nombre maximum de tentatives atteint');
  }
}
