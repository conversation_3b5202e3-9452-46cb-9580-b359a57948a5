import 'package:flutter/material.dart';
import '../../../admin_compagnie/services/admin_compagnie_auth_service.dart';
import '../../../admin_compagnie/services/auth_diagnostic_service.dart';
import '../../../admin_compagnie/services/firestore_repair_service.dart';
import '../../../admin_compagnie/services/direct_firestore_fix.dart';

/// 🏢 Écran de connexion spécialisé pour Admin Compagnie
class AdminCompagnieLoginScreen extends StatefulWidget {
  const AdminCompagnieLoginScreen({super.key});

  @override
  State<AdminCompagnieLoginScreen> createState() => _AdminCompagnieLoginScreenState();
}

class _AdminCompagnieLoginScreenState extends State<AdminCompagnieLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _passwordController = TextEditingController(text: 'AdminCompagnie123!');
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 🔐 Connexion avec le service spécialisé
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text.trim();

      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🚀 Début connexion: $email');

      final success = await AdminCompagnieAuthService.signInAndNavigate(
        context: context,
        email: email,
        password: password,
      );

      if (!success && mounted) {
        setState(() => _isLoading = false);
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur: $e');
      
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🧪 Test avec credentials par défaut
  Future<void> _testDefaultLogin() async {
    setState(() => _isLoading = true);

    try {
      final success = await AdminCompagnieAuthService.testDefaultLogin(context);
      
      if (!success && mounted) {
        setState(() => _isLoading = false);
      }

    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur test: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔍 Diagnostic
  Future<void> _runDiagnostic() async {
    await AuthDiagnosticService.testAdminCompagnieLogin();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔍 Diagnostic terminé - Voir les logs'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  /// 🔧 Réparation automatique (ancienne méthode)
  Future<void> _repairFirestore() async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🔧 Début réparation Firestore...');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🔧 Réparation en cours...'),
            backgroundColor: Colors.blue,
          ),
        );
      }

      final success = await FirestoreRepairService.fullRepair();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '✅ Réparation réussie !' : '❌ Échec de la réparation'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] ✅ Réparation réussie, tentative de connexion...');
        await _testDefaultLogin();
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur réparation: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur réparation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🔧 Réparation directe (nouvelle méthode)
  Future<void> _directFirestoreFix() async {
    try {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🔧 Début réparation directe...');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🔧 Réparation directe en cours...'),
            backgroundColor: Colors.purple,
          ),
        );
      }

      // Diagnostic avant réparation
      await DirectFirestoreFix.fullDiagnostic();

      // Réparation
      final success = await DirectFirestoreFix.fullFix();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '✅ Réparation directe réussie !' : '❌ Échec de la réparation directe'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }

      if (success) {
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] ✅ Réparation directe réussie !');
        debugPrint('[ADMIN_COMPAGNIE_LOGIN] 🎯 Vous pouvez maintenant naviguer vers le dashboard');

        // Navigation directe vers le dashboard
        if (mounted) {
          Navigator.pushReplacementNamed(context, '/compagnie-dashboard');
        }
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE_LOGIN] ❌ Erreur réparation directe: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur réparation directe: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: const Text('Connexion Admin Compagnie'),
        backgroundColor: const Color(0xFF10B981),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Logo et titre
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.admin_panel_settings_rounded,
                          size: 48,
                          color: Color(0xFF10B981),
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'Administration Compagnie',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1E293B),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Accès sécurisé au dashboard de gestion',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Formulaire de connexion
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Email
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        decoration: InputDecoration(
                          labelText: 'Email administrateur',
                          hintText: '<EMAIL>',
                          prefixIcon: const Icon(Icons.email_rounded),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Email requis';
                          }
                          if (!RegExp(r'^[^@]+@[^@]+\.[^@]+$').hasMatch(value)) {
                            return 'Format email invalide';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Mot de passe
                      TextFormField(
                        controller: _passwordController,
                        obscureText: _obscurePassword,
                        decoration: InputDecoration(
                          labelText: 'Mot de passe',
                          hintText: 'Votre mot de passe sécurisé',
                          prefixIcon: const Icon(Icons.lock_rounded),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword ? Icons.visibility_rounded : Icons.visibility_off_rounded,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.grey[50],
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Mot de passe requis';
                          }
                          if (value.length < 6) {
                            return 'Minimum 6 caractères';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 24),

                      // Bouton de connexion
                      SizedBox(
                        height: 50,
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _handleLogin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF10B981),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Se connecter',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Boutons de test et diagnostic
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.2)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      const Text(
                        '🧪 Outils de développement',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      
                      // Bouton test rapide
                      OutlinedButton.icon(
                        onPressed: _isLoading ? null : _testDefaultLogin,
                        icon: const Icon(Icons.flash_on_rounded),
                        label: const Text('Test Connexion Rapide'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange,
                          side: const BorderSide(color: Colors.orange),
                        ),
                      ),
                      
                      const SizedBox(height: 8),

                      // Bouton diagnostic
                      OutlinedButton.icon(
                        onPressed: _runDiagnostic,
                        icon: const Icon(Icons.bug_report_rounded),
                        label: const Text('Diagnostic Complet'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.blue,
                          side: const BorderSide(color: Colors.blue),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Bouton réparation directe (SOLUTION)
                      ElevatedButton.icon(
                        onPressed: _directFirestoreFix,
                        icon: const Icon(Icons.auto_fix_high_rounded),
                        label: const Text('🚀 SOLUTION DIRECTE'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Bouton réparation (ancienne méthode)
                      OutlinedButton.icon(
                        onPressed: _repairFirestore,
                        icon: const Icon(Icons.build_rounded),
                        label: const Text('🔧 Réparer Firestore'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                  ),
                  child: const Text(
                    '🔐 Connexion sécurisée avec service spécialisé Admin Compagnie',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
