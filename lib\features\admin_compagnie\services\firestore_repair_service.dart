import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

/// 🔧 Service de réparation Firestore pour Admin Compagnie
class FirestoreRepairService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🔧 Créer le document Firestore manquant pour Admin Compagnie
  static Future<bool> createMissingAdminCompagnieDocument() async {
    try {
      debugPrint('[FIRESTORE_REPAIR] 🔧 Début réparation document Admin Compagnie...');

      // 1. Vérifier l'utilisateur connecté
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('[FIRESTORE_REPAIR] ❌ Aucun utilisateur connecté');
        return false;
      }

      debugPrint('[FIRESTORE_REPAIR] ✅ Utilisateur connecté: ${user.uid}');

      // 2. Vérifier si le document existe déjà
      final existingDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (existingDoc.exists) {
        debugPrint('[FIRESTORE_REPAIR] ⚠️ Document existe déjà');
        final data = existingDoc.data()!;
        debugPrint('[FIRESTORE_REPAIR] 📋 Données existantes: ${data['role']}');
        return true;
      }

      // 3. Créer d'abord une compagnie de test si nécessaire
      final compagnieId = await _createTestCompagnieIfNeeded();

      // 4. Créer le document utilisateur
      final userData = {
        'email': user.email ?? '<EMAIL>',
        'nom': 'Admin',
        'prenom': 'Compagnie',
        'role': 'admin_compagnie',
        'status': 'actif',
        'compagnieId': compagnieId,
        'compagnieNom': 'STAR Assurance',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'firestore_repair_service',
        'isFakeData': true,
        'last_login': null,
        'password_reset_required': false,
      };

      await _firestore
          .collection('users')
          .doc(user.uid)
          .set(userData);

      debugPrint('[FIRESTORE_REPAIR] ✅ Document utilisateur créé avec succès');
      debugPrint('[FIRESTORE_REPAIR] 📋 Données: $userData');

      return true;

    } catch (e) {
      debugPrint('[FIRESTORE_REPAIR] ❌ Erreur réparation: $e');
      return false;
    }
  }

  /// 🏢 Créer une compagnie de test si nécessaire
  static Future<String> _createTestCompagnieIfNeeded() async {
    try {
      const compagnieId = 'star-assurance-test';
      
      // Vérifier si la compagnie existe
      final existingCompagnie = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (existingCompagnie.exists) {
        debugPrint('[FIRESTORE_REPAIR] ✅ Compagnie existe déjà: $compagnieId');
        return compagnieId;
      }

      // Créer la compagnie
      final compagnieData = {
        'nom': 'STAR Assurance',
        'code': 'STAR',
        'type': 'compagnie_assurance',
        'status': 'actif',
        'adresse': '123 Avenue Habib Bourguiba, Tunis',
        'telephone': '+216 71 123 456',
        'email': '<EMAIL>',
        'site_web': 'www.star-assurance.tn',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'firestore_repair_service',
        'isFakeData': true,
      };

      await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .set(compagnieData);

      debugPrint('[FIRESTORE_REPAIR] ✅ Compagnie créée: $compagnieId');
      return compagnieId;

    } catch (e) {
      debugPrint('[FIRESTORE_REPAIR] ❌ Erreur création compagnie: $e');
      return 'star-assurance-test'; // Retourner un ID par défaut
    }
  }

  /// 🔍 Vérifier l'état du document Admin Compagnie
  static Future<Map<String, dynamic>> checkAdminCompagnieDocument() async {
    try {
      final user = _auth.currentUser;
      
      if (user == null) {
        return {
          'exists': false,
          'error': 'Aucun utilisateur connecté',
        };
      }

      final doc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!doc.exists) {
        return {
          'exists': false,
          'uid': user.uid,
          'email': user.email,
          'error': 'Document Firestore manquant',
        };
      }

      final data = doc.data()!;
      return {
        'exists': true,
        'uid': user.uid,
        'data': data,
        'role': data['role'],
        'compagnieId': data['compagnieId'],
      };

    } catch (e) {
      return {
        'exists': false,
        'error': 'Erreur vérification: $e',
      };
    }
  }

  /// 🔧 Réparation complète avec diagnostic
  static Future<bool> fullRepair() async {
    try {
      debugPrint('[FIRESTORE_REPAIR] 🚀 Début réparation complète...');

      // 1. Diagnostic initial
      final initialCheck = await checkAdminCompagnieDocument();
      debugPrint('[FIRESTORE_REPAIR] 📋 État initial: $initialCheck');

      if (initialCheck['exists'] == true) {
        debugPrint('[FIRESTORE_REPAIR] ✅ Document existe déjà, pas de réparation nécessaire');
        return true;
      }

      // 2. Créer le document manquant
      final created = await createMissingAdminCompagnieDocument();
      
      if (!created) {
        debugPrint('[FIRESTORE_REPAIR] ❌ Échec création document');
        return false;
      }

      // 3. Vérification finale
      await Future.delayed(const Duration(milliseconds: 1000)); // Attendre la propagation
      final finalCheck = await checkAdminCompagnieDocument();
      debugPrint('[FIRESTORE_REPAIR] 📋 État final: $finalCheck');

      if (finalCheck['exists'] == true) {
        debugPrint('[FIRESTORE_REPAIR] ✅ Réparation complète réussie !');
        return true;
      } else {
        debugPrint('[FIRESTORE_REPAIR] ❌ Réparation échouée');
        return false;
      }

    } catch (e) {
      debugPrint('[FIRESTORE_REPAIR] ❌ Erreur réparation complète: $e');
      return false;
    }
  }

  /// 🧹 Nettoyer les données de test
  static Future<void> cleanTestData() async {
    try {
      debugPrint('[FIRESTORE_REPAIR] 🧹 Nettoyage données de test...');

      final user = _auth.currentUser;
      if (user != null) {
        // Supprimer le document utilisateur s'il est marqué comme test
        final userDoc = await _firestore
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists && userDoc.data()!['isFakeData'] == true) {
          await userDoc.reference.delete();
          debugPrint('[FIRESTORE_REPAIR] ✅ Document utilisateur test supprimé');
        }
      }

      // Supprimer la compagnie de test
      await _firestore
          .collection('compagnies_assurance')
          .doc('star-assurance-test')
          .delete();

      debugPrint('[FIRESTORE_REPAIR] ✅ Nettoyage terminé');

    } catch (e) {
      debugPrint('[FIRESTORE_REPAIR] ❌ Erreur nettoyage: $e');
    }
  }
}
