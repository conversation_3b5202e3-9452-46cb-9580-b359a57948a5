import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';

/// 📧 Service d'email simplifié pour les notifications critiques
class SimpleEmailService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseFunctions _functions = FirebaseFunctions.instanceFor(region: 'europe-west1');

  /// 📧 Envoyer un email de création de compte (méthode simplifiée)
  static Future<bool> sendAccountCreatedEmail({
    required String userEmail,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
  }) async {
    try {
      debugPrint('[SIMPLE_EMAIL] 📧 Envoi email création compte à: $userEmail');

      // 1. Essayer d'abord avec la Cloud Function existante
      final cloudFunctionResult = await _tryCloudFunction(
        userEmail: userEmail,
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: role,
        companyName: companyName,
      );

      if (cloudFunctionResult) {
        debugPrint('[SIMPLE_EMAIL] ✅ Email envoyé via Cloud Function');
        await _logEmailSuccess(userEmail, 'cloud_function');
        return true;
      }

      // 2. Si Cloud Function échoue, essayer la méthode Gmail directe
      final gmailResult = await _tryGmailDirect(
        userEmail: userEmail,
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: role,
        companyName: companyName,
      );

      if (gmailResult) {
        debugPrint('[SIMPLE_EMAIL] ✅ Email envoyé via Gmail direct');
        await _logEmailSuccess(userEmail, 'gmail_direct');
        return true;
      }

      // 3. Si tout échoue, enregistrer dans Firestore pour traitement manuel
      await _saveForManualProcessing(
        userEmail: userEmail,
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: role,
        companyName: companyName,
      );

      debugPrint('[SIMPLE_EMAIL] ⚠️ Email sauvegardé pour traitement manuel');
      return false;

    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Erreur envoi email: $e');
      await _logEmailError(userEmail, e.toString());
      return false;
    }
  }

  /// 🔧 Essayer d'envoyer via Cloud Function
  static Future<bool> _tryCloudFunction({
    required String userEmail,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
  }) async {
    try {
      debugPrint('[SIMPLE_EMAIL] 🔄 Tentative Cloud Function (région: europe-west1)...');
      debugPrint('[SIMPLE_EMAIL] 📧 Destinataire: $userEmail');
      debugPrint('[SIMPLE_EMAIL] 👤 Utilisateur: $userName');
      debugPrint('[SIMPLE_EMAIL] 🏷️ Rôle: $role');

      final callable = _functions.httpsCallable('sendEmail');

      final emailHtml = _generateEmailHtml(
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: role,
        companyName: companyName,
      );

      debugPrint('[SIMPLE_EMAIL] 📤 Appel de la Cloud Function...');

      final result = await callable.call({
        'to': userEmail,
        'subject': 'Bienvenue sur Constat Tunisie - Votre compte a été créé',
        'html': emailHtml,
        'text': 'Votre compte a été créé. Mot de passe temporaire: $temporaryPassword',
        'userName': userName,
        'temporaryPassword': temporaryPassword,
        'role': role,
        'companyName': companyName,
      });

      debugPrint('[SIMPLE_EMAIL] ✅ Réponse Cloud Function: ${result.data}');

      final success = result.data['success'] == true;
      if (success) {
        debugPrint('[SIMPLE_EMAIL] 🎉 Email envoyé avec succès via Cloud Function !');
      } else {
        debugPrint('[SIMPLE_EMAIL] ⚠️ Cloud Function retourne success=false');
      }

      return success;

    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Cloud Function échouée: $e');
      debugPrint('[SIMPLE_EMAIL] 🔍 Type d\'erreur: ${e.runtimeType}');

      // Analyser le type d'erreur
      if (e.toString().contains('not-found')) {
        debugPrint('[SIMPLE_EMAIL] 💡 La Cloud Function "sendEmail" n\'existe pas ou n\'est pas déployée');
      } else if (e.toString().contains('unauthenticated')) {
        debugPrint('[SIMPLE_EMAIL] 💡 Problème d\'authentification avec la Cloud Function');
      } else if (e.toString().contains('permission-denied')) {
        debugPrint('[SIMPLE_EMAIL] 💡 Permissions insuffisantes pour la Cloud Function');
      }

      return false;
    }
  }

  /// 📧 Essayer d'envoyer via Gmail direct (méthode de secours)
  static Future<bool> _tryGmailDirect({
    required String userEmail,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
  }) async {
    try {
      // Cette méthode utiliserait l'API Gmail directement
      // Pour l'instant, on simule juste l'envoi
      debugPrint('[SIMPLE_EMAIL] 🔄 Tentative Gmail direct...');
      
      // TODO: Implémenter l'envoi Gmail direct avec OAuth2
      // Utiliser le refresh token existant: 1//04fqCR47aG8PuCgYIARAAGAQSNwF-L9IrbmVfT1Ip925nf40rYtGez0sw_fJH341WZM9UHDhdWnkShe5AONoFyep4P6lS2E1VsFw
      
      return false; // Temporairement désactivé
    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Gmail direct échoué: $e');
      return false;
    }
  }

  /// 💾 Sauvegarder pour traitement manuel
  static Future<void> _saveForManualProcessing({
    required String userEmail,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
  }) async {
    try {
      await _firestore.collection('pending_emails').add({
        'type': 'account_created',
        'to': userEmail,
        'userName': userName,
        'temporaryPassword': temporaryPassword,
        'role': role,
        'companyName': companyName,
        'status': 'pending_manual',
        'createdAt': FieldValue.serverTimestamp(),
        'priority': 'high',
        'attempts': 0,
        'lastAttempt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Erreur sauvegarde: $e');
    }
  }

  /// 📝 Logger le succès d'envoi
  static Future<void> _logEmailSuccess(String email, String method) async {
    try {
      await _firestore.collection('email_logs').add({
        'email': email,
        'status': 'sent',
        'method': method,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Erreur log succès: $e');
    }
  }

  /// 📝 Logger l'erreur d'envoi
  static Future<void> _logEmailError(String email, String error) async {
    try {
      await _firestore.collection('email_logs').add({
        'email': email,
        'status': 'failed',
        'error': error,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Erreur log erreur: $e');
    }
  }

  /// 🎨 Générer le HTML de l'email
  static String _generateEmailHtml({
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
  }) {
    return '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .credentials { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .password { background: #fff3e0; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 18px; font-weight: bold; text-align: center; margin: 10px 0; }
        .btn { display: inline-block; background: #4CAF50; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Bienvenue sur Constat Tunisie</h1>
            <p>Votre compte a été créé avec succès</p>
        </div>
        <div class="content">
            <h2>Bonjour $userName 👋</h2>
            <p>Votre compte <strong>$role</strong> a été créé avec succès${companyName != null ? ' pour $companyName' : ''}.</p>

            <div class="credentials">
                <h3>🔑 Vos identifiants de connexion :</h3>
                <p><strong>Email :</strong> Votre adresse email</p>
                <p><strong>Mot de passe temporaire :</strong></p>
                <div class="password">$temporaryPassword</div>
            </div>

            <div class="warning">
                <strong>⚠️ Important :</strong> Ce mot de passe est temporaire. Vous devrez le changer lors de votre première connexion.
            </div>

            <p style="text-align: center;">
                <a href="https://constat-tunisie.com/login" class="btn">Se connecter maintenant</a>
            </p>

            <h3>📋 Prochaines étapes :</h3>
            <ol>
                <li>Cliquez sur le bouton ci-dessus pour vous connecter</li>
                <li>Utilisez votre email et le mot de passe temporaire</li>
                <li>Changez votre mot de passe lors de la première connexion</li>
                <li>Explorez votre tableau de bord</li>
            </ol>

            <hr style="margin: 30px 0;">
            <p style="text-align: center; color: #666; font-size: 12px;">
                Email envoyé automatiquement par Constat Tunisie<br>
                📧 <EMAIL> | 📞 +216 70 000 000
            </p>
        </div>
    </div>
</body>
</html>
    ''';
  }

  /// 📊 Obtenir les statistiques d'envoi d'emails
  static Future<Map<String, dynamic>> getEmailStats() async {
    try {
      final logsQuery = await _firestore
          .collection('email_logs')
          .orderBy('timestamp', descending: true)
          .limit(100)
          .get();

      final pendingQuery = await _firestore
          .collection('pending_emails')
          .where('status', isEqualTo: 'pending_manual')
          .get();

      return {
        'total_attempts': logsQuery.docs.length,
        'successful_sends': logsQuery.docs.where((doc) => doc.data()['status'] == 'sent').length,
        'failed_sends': logsQuery.docs.where((doc) => doc.data()['status'] == 'failed').length,
        'pending_manual': pendingQuery.docs.length,
        'last_24h': logsQuery.docs.where((doc) {
          final timestamp = doc.data()['timestamp'] as Timestamp?;
          if (timestamp == null) return false;
          final diff = DateTime.now().difference(timestamp.toDate());
          return diff.inHours <= 24;
        }).length,
      };
    } catch (e) {
      debugPrint('[SIMPLE_EMAIL] ❌ Erreur stats: $e');
      return {};
    }
  }
}
