import 'package:flutter/material.dart';
import '../presentation/screens/company_dashboard_screen.dart';
import '../services/company_security_middleware.dart';

/// 🛣️ Routes sécurisées pour les dashboards de compagnies
class CompanyRoutes {
  
  /// 📱 Générateur de routes pour les compagnies
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final uri = Uri.parse(settings.name ?? '');
    
    // Route: /compagnie-dashboard/{compagnieId}
    if (uri.pathSegments.length == 2 && 
        uri.pathSegments[0] == 'compagnie-dashboard') {
      
      final compagnieId = uri.pathSegments[1];
      
      return MaterialPageRoute(
        builder: (context) => SecureCompanyDashboard(compagnieId: compagnieId),
        settings: settings,
      );
    }
    
    return null;
  }
  
  /// 🔐 Widget sécurisé pour le dashboard compagnie
  static Widget buildSecureCompanyDashboard(String compagnieId) {
    return SecureCompanyDashboard(compagnieId: compagnieId);
  }
}

/// 🛡️ Dashboard sécurisé avec validation d'accès
class SecureCompanyDashboard extends StatefulWidget {
  final String compagnieId;
  
  const SecureCompanyDashboard({
    super.key,
    required this.compagnieId,
  });
  
  @override
  State<SecureCompanyDashboard> createState() => _SecureCompanyDashboardState();
}

class _SecureCompanyDashboardState extends State<SecureCompanyDashboard> {
  bool _isValidating = true;
  bool _hasAccess = false;
  String? _errorMessage;
  
  @override
  void initState() {
    super.initState();
    _validateAccess();
  }
  
  /// 🔐 Valider l'accès avant d'afficher le dashboard
  Future<void> _validateAccess() async {
    try {
      final result = await CompanySecurityMiddleware.checkCompanyResourceAccess(
        compagnieId: widget.compagnieId,
        resourceType: 'company_dashboard',
      );
      
      setState(() {
        _isValidating = false;
        _hasAccess = result.isGranted;
        _errorMessage = result.reason;
      });
      
      // Si l'accès est refusé, rediriger vers login après un délai
      if (!result.isGranted) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/login');
          }
        });
      }
      
    } catch (e) {
      setState(() {
        _isValidating = false;
        _hasAccess = false;
        _errorMessage = 'Erreur de validation: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isValidating) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('🔐 Validation des accès...'),
            ],
          ),
        ),
      );
    }
    
    if (!_hasAccess) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Accès Refusé'),
          backgroundColor: Colors.red,
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.security,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 24),
                const Text(
                  '🚫 Accès Refusé',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  _errorMessage ?? 'Vous n\'avez pas l\'autorisation d\'accéder à cette compagnie.',
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Redirection vers la page de connexion...',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),
                const LinearProgressIndicator(),
              ],
            ),
          ),
        ),
      );
    }
    
    // Accès autorisé - Afficher le dashboard
    return CompanyDashboardScreen(compagnieId: widget.compagnieId);
  }
}

/// 🎯 Extensions pour faciliter la navigation sécurisée
extension SecureCompanyNavigation on BuildContext {
  
  /// 🏢 Naviguer vers le dashboard d'une compagnie (avec validation)
  Future<void> navigateToCompanyDashboard(String compagnieId) async {
    // Vérifier l'accès avant la navigation
    final hasAccess = await CompanySecurityMiddleware.validateRouteAccess(
      route: '/compagnie-dashboard/$compagnieId',
      routeParams: {'compagnieId': compagnieId},
    );
    
    if (hasAccess) {
      Navigator.pushNamed(this, '/compagnie-dashboard/$compagnieId');
    } else {
      // Afficher un message d'erreur
      ScaffoldMessenger.of(this).showSnackBar(
        const SnackBar(
          content: Text('🚫 Accès refusé à cette compagnie'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// 🔄 Rediriger automatiquement selon le rôle de l'utilisateur
  Future<void> redirectBasedOnUserRole() async {
    try {
      final currentUserInfo = await CompanyRedirectService.getCurrentUserCompanyInfo();
      
      if (currentUserInfo == null) {
        Navigator.pushReplacementNamed(this, '/login');
        return;
      }
      
      final userRole = currentUserInfo['userRole'] as String?;
      
      switch (userRole) {
        case 'super_admin':
          Navigator.pushReplacementNamed(this, '/admin-dashboard');
          break;
          
        case 'admin_compagnie':
          final compagnieId = currentUserInfo['id'] as String;
          Navigator.pushReplacementNamed(this, '/compagnie-dashboard/$compagnieId');
          break;
          
        case 'admin_agence':
          // TODO: Implémenter la redirection agence
          Navigator.pushReplacementNamed(this, '/agence-dashboard');
          break;
          
        case 'agent':
          Navigator.pushReplacementNamed(this, '/agent-dashboard');
          break;
          
        case 'expert':
          Navigator.pushReplacementNamed(this, '/expert-dashboard');
          break;
          
        case 'conducteur':
          Navigator.pushReplacementNamed(this, '/conducteur-dashboard');
          break;
          
        default:
          Navigator.pushReplacementNamed(this, '/login');
      }
      
    } catch (e) {
      debugPrint('Erreur redirection automatique: $e');
      Navigator.pushReplacementNamed(this, '/login');
    }
  }
}

/// 📋 Configuration des routes de compagnies
class CompanyRoutesConfig {
  
  /// 🗺️ Map des routes disponibles
  static const Map<String, String> routes = {
    'companyDashboard': '/compagnie-dashboard',
    'companyAgents': '/compagnie-dashboard/{id}/agents',
    'companyAgencies': '/compagnie-dashboard/{id}/agences',
    'companyContracts': '/compagnie-dashboard/{id}/contrats',
    'companyClaims': '/compagnie-dashboard/{id}/sinistres',
    'companyReports': '/compagnie-dashboard/{id}/rapports',
    'companySettings': '/compagnie-dashboard/{id}/parametres',
  };
  
  /// 🔐 Routes nécessitant une validation de sécurité
  static const List<String> secureRoutes = [
    '/compagnie-dashboard',
    '/compagnie-dashboard/{id}/agents',
    '/compagnie-dashboard/{id}/agences',
    '/compagnie-dashboard/{id}/contrats',
    '/compagnie-dashboard/{id}/sinistres',
    '/compagnie-dashboard/{id}/rapports',
    '/compagnie-dashboard/{id}/parametres',
  ];
  
  /// ✅ Vérifier si une route nécessite une validation de sécurité
  static bool requiresSecurityValidation(String route) {
    return secureRoutes.any((secureRoute) => 
      route.startsWith(secureRoute.split('{').first));
  }
  
  /// 🏢 Extraire le compagnieId d'une route
  static String? extractCompagnieId(String route) {
    final regex = RegExp(r'/compagnie-dashboard/([^/]+)');
    final match = regex.firstMatch(route);
    return match?.group(1);
  }
}

/// 🛡️ Middleware de route pour la sécurité des compagnies
class CompanyRouteMiddleware {
  
  /// 🔍 Valider une route avant navigation
  static Future<bool> validateRoute(String route) async {
    if (!CompanyRoutesConfig.requiresSecurityValidation(route)) {
      return true; // Route publique
    }
    
    final compagnieId = CompanyRoutesConfig.extractCompagnieId(route);
    if (compagnieId == null) {
      return false; // Route malformée
    }
    
    return await CompanySecurityMiddleware.validateRouteAccess(
      route: route,
      routeParams: {'compagnieId': compagnieId},
    );
  }
  
  /// 🚫 Gérer l'accès refusé
  static void handleAccessDenied(BuildContext context, String route) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.security, color: Colors.red),
            SizedBox(width: 8),
            Text('Accès Refusé'),
          ],
        ),
        content: Text(
          'Vous n\'avez pas l\'autorisation d\'accéder à cette ressource.\n\nRoute: $route',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/login');
            },
            child: const Text('Retour à la connexion'),
          ),
        ],
      ),
    );
  }
}
