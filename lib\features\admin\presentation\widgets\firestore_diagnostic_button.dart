import 'package:flutter/material.dart';
import '../../../../core/services/firestore_diagnostic_service.dart';
import '../../../../core/theme/modern_theme.dart';

/// 🔍 Bouton de diagnostic Firestore
/// 
/// Permet de diagnostiquer pourquoi Firestore semble indisponible
class FirestoreDiagnosticButton extends StatefulWidget {
  const FirestoreDiagnosticButton({Key? key}) : super(key: key);

  @override
  State<FirestoreDiagnosticButton> createState() => _FirestoreDiagnosticButtonState();
}

class _FirestoreDiagnosticButtonState extends State<FirestoreDiagnosticButton> {
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: _isRunning ? null : _runDiagnostic,
      icon: _isRunning 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.health_and_safety),
      label: Text(_isRunning ? 'Diagnostic...' : 'Diagnostic Firestore'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// 🔍 Lancer le diagnostic
  Future<void> _runDiagnostic() async {
    setState(() => _isRunning = true);

    try {
      final diagnostic = await FirestoreDiagnosticService.performFullDiagnostic();
      
      if (mounted) {
        _showDiagnosticResults(diagnostic);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// 📊 Afficher les résultats du diagnostic
  void _showDiagnosticResults(Map<String, dynamic> diagnostic) {
    final overallStatus = diagnostic['overall_status'] as String;
    final tests = diagnostic['tests'] as Map<String, dynamic>;
    final recommendations = diagnostic['recommendations'] as List<dynamic>;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getStatusIcon(overallStatus),
              color: _getStatusColor(overallStatus),
            ),
            const SizedBox(width: 8),
            const Text('Diagnostic Firestore'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Statut global
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _getStatusColor(overallStatus).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _getStatusIcon(overallStatus),
                        color: _getStatusColor(overallStatus),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Statut: ${_getStatusText(overallStatus)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(overallStatus),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Tests détaillés
                const Text(
                  'Tests effectués:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                
                ...tests.entries.map((entry) {
                  final testName = entry.key;
                  final result = entry.value as Map<String, dynamic>;
                  final status = result['status'] as String;
                  final message = result['message'] as String;
                  
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: [
                        Icon(
                          _getStatusIcon(status),
                          color: _getStatusColor(status),
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '$testName: $message',
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                
                const SizedBox(height: 16),
                
                // Recommandations
                if (recommendations.isNotEmpty) ...[
                  const Text(
                    'Recommandations:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  
                  ...recommendations.map((rec) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                          Expanded(
                            child: Text(
                              rec.toString(),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          if (overallStatus != 'healthy')
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _runDiagnostic(); // Relancer le diagnostic
              },
              child: const Text('Relancer'),
            ),
        ],
      ),
    );
  }

  /// 🎨 Obtenir l'icône selon le statut
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'success':
      case 'healthy':
        return Icons.check_circle;
      case 'warning':
      case 'degraded':
        return Icons.warning;
      case 'failed':
      case 'error':
      case 'unhealthy':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  /// 🎨 Obtenir la couleur selon le statut
  Color _getStatusColor(String status) {
    switch (status) {
      case 'success':
      case 'healthy':
        return Colors.green;
      case 'warning':
      case 'degraded':
        return Colors.orange;
      case 'failed':
      case 'error':
      case 'unhealthy':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 📝 Obtenir le texte selon le statut
  String _getStatusText(String status) {
    switch (status) {
      case 'healthy':
        return 'Excellent';
      case 'degraded':
        return 'Dégradé';
      case 'unhealthy':
        return 'Problématique';
      default:
        return status;
    }
  }
}
