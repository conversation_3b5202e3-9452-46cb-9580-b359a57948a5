import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

/// 📧 Service d'envoi d'emails direct via Gmail API
/// 
/// Utilise votre token OAuth2 pour envoyer des emails directement
/// sans dépendre des Cloud Functions Firebase
class GmailDirectService {
  // Votre refresh token OAuth2 Gmail
  static const String _refreshToken = '1//04fqCR47aG8PuCgYIARAAGAQSNwF-L9IrbmVfT1Ip925nf40rYtGez0sw_fJH341WZM9UHDhdWnkShe5AONoFyep4P6lS2E1VsFw';
  
  // Client ID Android OAuth2
  static const String _clientId = '*************-bcja6qd5feh9rpndg3klveh1pcihruj5.apps.googleusercontent.com';
  
  // Client Secret (à obtenir depuis Google Cloud Console)
  static const String _clientSecret = 'GOCSPX-YOUR_CLIENT_SECRET'; // TODO: Remplacer par votre client secret
  
  // Email expéditeur
  static const String _senderEmail = '<EMAIL>';
  static const String _senderName = 'Constat Tunisie';

  /// 📧 Envoyer un email de création de compte
  static Future<Map<String, dynamic>> sendAccountCreatedEmail({
    required String userEmail,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
    String? agencyName,
  }) async {
    try {
      debugPrint('[GMAIL_DIRECT] 📧 Envoi email création compte à: $userEmail');
      debugPrint('[GMAIL_DIRECT] 👤 Utilisateur: $userName');
      debugPrint('[GMAIL_DIRECT] 🏷️ Rôle: $role');

      // 1. Obtenir un access token
      final accessTokenResult = await _getAccessToken();
      if (!accessTokenResult['success']) {
        return {
          'success': false,
          'error': 'Impossible d\'obtenir le token d\'accès: ${accessTokenResult['error']}',
        };
      }

      final accessToken = accessTokenResult['access_token'];

      // 2. Générer le contenu HTML de l'email
      final emailHtml = _generateAccountCreatedEmailHtml(
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: role,
        companyName: companyName,
        agencyName: agencyName,
      );

      // 3. Créer le message email au format RFC 2822
      final emailMessage = _createEmailMessage(
        to: userEmail,
        subject: 'Bienvenue sur Constat Tunisie - Votre compte a été créé',
        htmlContent: emailHtml,
      );

      // 4. Encoder en base64
      final encodedMessage = base64Url.encode(utf8.encode(emailMessage));

      // 5. Envoyer via Gmail API
      final response = await http.post(
        Uri.parse('https://gmail.googleapis.com/gmail/v1/users/me/messages/send'),
        headers: {
          'Authorization': 'Bearer $accessToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'raw': encodedMessage,
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('[GMAIL_DIRECT] ✅ Email envoyé avec succès !');
        return {
          'success': true,
          'message': 'Email envoyé avec succès',
          'messageId': jsonDecode(response.body)['id'],
        };
      } else {
        debugPrint('[GMAIL_DIRECT] ❌ Erreur envoi: ${response.statusCode} - ${response.body}');
        return {
          'success': false,
          'error': 'Erreur Gmail API: ${response.statusCode} - ${response.body}',
        };
      }

    } catch (e) {
      debugPrint('[GMAIL_DIRECT] ❌ Erreur envoi email: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🔑 Obtenir un access token à partir du refresh token
  static Future<Map<String, dynamic>> _getAccessToken() async {
    try {
      final response = await http.post(
        Uri.parse('https://oauth2.googleapis.com/token'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'client_id': _clientId,
          'client_secret': _clientSecret,
          'refresh_token': _refreshToken,
          'grant_type': 'refresh_token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'access_token': data['access_token'],
          'expires_in': data['expires_in'],
        };
      } else {
        return {
          'success': false,
          'error': 'Erreur OAuth2: ${response.statusCode} - ${response.body}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 📝 Créer le message email au format RFC 2822
  static String _createEmailMessage({
    required String to,
    required String subject,
    required String htmlContent,
  }) {
    final message = '''To: $to
From: $_senderName <$_senderEmail>
Subject: $subject
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

$htmlContent''';

    return message;
  }

  /// 🎨 Générer le HTML de l'email de création de compte
  static String _generateAccountCreatedEmailHtml({
    required String userName,
    required String temporaryPassword,
    required String role,
    String? companyName,
    String? agencyName,
  }) {
    final roleDisplayName = _getRoleDisplayName(role);
    final organizationInfo = _getOrganizationInfo(role, companyName, agencyName);

    return '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenue sur Constat Tunisie</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: 300; }
        .content { padding: 40px 30px; }
        .welcome-box { background-color: #f8f9fa; border-left: 4px solid #667eea; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .credentials-box { background-color: #e8f4fd; border: 2px solid #2196F3; padding: 20px; margin: 20px 0; border-radius: 8px; text-align: center; }
        .password { font-family: 'Courier New', monospace; font-size: 24px; font-weight: bold; color: #d32f2f; background-color: #fff; padding: 10px; border-radius: 5px; border: 2px dashed #d32f2f; display: inline-block; margin: 10px 0; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .btn { display: inline-block; background-color: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .info-item { background-color: #f8f9fa; padding: 15px; border-radius: 5px; }
        .info-label { font-weight: bold; color: #495057; font-size: 12px; text-transform: uppercase; }
        .info-value { color: #212529; font-size: 16px; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Constat Tunisie</h1>
            <p>Plateforme de Gestion des Sinistres Automobiles</p>
        </div>
        
        <div class="content">
            <div class="welcome-box">
                <h2>🎉 Bienvenue, $userName !</h2>
                <p>Votre compte <strong>$roleDisplayName</strong> a été créé avec succès sur la plateforme Constat Tunisie.</p>
                $organizationInfo
            </div>

            <div class="credentials-box">
                <h3>🔐 Vos identifiants de connexion</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Email de connexion</div>
                        <div class="info-value">${userName.split(' ').first.toLowerCase()}@constat-tunisie.tn</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Rôle</div>
                        <div class="info-value">$roleDisplayName</div>
                    </div>
                </div>
                
                <p><strong>Mot de passe temporaire :</strong></p>
                <div class="password">$temporaryPassword</div>
                
                <div class="warning">
                    ⚠️ <strong>Important :</strong> Ce mot de passe est temporaire. Vous devrez le changer lors de votre première connexion pour des raisons de sécurité.
                </div>
            </div>

            <div style="text-align: center;">
                <a href="https://constat-tunisie.tn/login" class="btn">🚀 Se connecter maintenant</a>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <h4>📱 Prochaines étapes :</h4>
                <ol>
                    <li>Connectez-vous avec vos identifiants</li>
                    <li>Changez votre mot de passe temporaire</li>
                    <li>Complétez votre profil</li>
                    <li>Explorez votre tableau de bord</li>
                </ol>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 Constat Tunisie - Plateforme de Gestion des Sinistres</p>
            <p>📧 Support: <EMAIL> | 📞 +216 XX XXX XXX</p>
            <p style="font-size: 12px; color: #adb5bd;">
                Cet email a été envoyé automatiquement. Merci de ne pas répondre à ce message.
            </p>
        </div>
    </div>
</body>
</html>''';
  }

  /// 🏷️ Obtenir le nom d'affichage du rôle
  static String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_compagnie':
        return 'Administrateur Compagnie';
      case 'admin_agence':
        return 'Administrateur Agence';
      case 'agent_agence':
        return 'Agent d\'Assurance';
      case 'expert_auto':
        return 'Expert Automobile';
      case 'conducteur':
        return 'Conducteur';
      default:
        return 'Utilisateur';
    }
  }

  /// 🏢 Obtenir les informations d'organisation
  static String _getOrganizationInfo(String role, String? companyName, String? agencyName) {
    if (role == 'admin_compagnie' && companyName != null) {
      return '<p>🏢 <strong>Compagnie :</strong> $companyName</p>';
    } else if (role == 'admin_agence' && companyName != null && agencyName != null) {
      return '<p>🏢 <strong>Compagnie :</strong> $companyName<br>🏪 <strong>Agence :</strong> $agencyName</p>';
    } else if (role == 'agent_agence' && agencyName != null) {
      return '<p>🏪 <strong>Agence :</strong> $agencyName</p>';
    }
    return '';
  }

  /// 🧪 Tester l'envoi d'email
  static Future<Map<String, dynamic>> testEmailSending() async {
    return await sendAccountCreatedEmail(
      userEmail: '<EMAIL>',
      userName: 'Test User',
      temporaryPassword: 'TestPass123!',
      role: 'admin_compagnie',
      companyName: 'Test Company',
    );
  }
}
