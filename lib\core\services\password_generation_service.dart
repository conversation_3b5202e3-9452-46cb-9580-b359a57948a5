import 'dart:math';

/// 🔐 Service de génération de mots de passe sécurisés
class PasswordGenerationService {
  
  /// 🔑 Générer un mot de passe sécurisé
  static String generateSecurePassword({int length = 12}) {
    const String lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const String uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const String numbers = '0123456789';
    const String symbols = '!@#\$%^&*()_+-=[]{}|;:,.<>?';
    
    final Random random = Random.secure();
    String password = '';
    
    // Assurer au moins un caractère de chaque type
    password += lowercase[random.nextInt(lowercase.length)];
    password += uppercase[random.nextInt(uppercase.length)];
    password += numbers[random.nextInt(numbers.length)];
    password += symbols[random.nextInt(symbols.length)];
    
    // Compléter avec des caractères aléatoires
    const String allChars = lowercase + uppercase + numbers + symbols;
    for (int i = password.length; i < length; i++) {
      password += allChars[random.nextInt(allChars.length)];
    }
    
    // Mélanger les caractères
    List<String> passwordList = password.split('');
    passwordList.shuffle(random);
    
    return passwordList.join('');
  }
  
  /// 📊 Vérifier la force d'un mot de passe
  static Map<String, dynamic> checkPasswordStrength(String password) {
    int score = 0;
    List<String> feedback = [];
    
    // Longueur
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.add('Au moins 8 caractères requis');
    }
    
    if (password.length >= 12) {
      score += 1;
    }
    
    // Minuscules
    if (RegExp(r'[a-z]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Au moins une minuscule requise');
    }
    
    // Majuscules
    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Au moins une majuscule requise');
    }
    
    // Chiffres
    if (RegExp(r'[0-9]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Au moins un chiffre requis');
    }
    
    // Symboles
    if (RegExp(r'[!@#\$%^&*()_+\-=\[\]{}|;:,.<>?]').hasMatch(password)) {
      score += 1;
    } else {
      feedback.add('Au moins un symbole requis');
    }
    
    // Déterminer le niveau
    String level;
    if (score <= 2) {
      level = 'Faible';
    } else if (score <= 4) {
      level = 'Moyen';
    } else {
      level = 'Fort';
    }
    
    return {
      'score': score,
      'level': level,
      'feedback': feedback,
      'isStrong': score >= 5,
    };
  }
  
  /// 🎲 Générer un mot de passe mémorable
  static String generateMemorablePassword() {
    const List<String> adjectives = [
      'Rapide', 'Brillant', 'Magique', 'Secret', 'Puissant',
      'Elegant', 'Moderne', 'Solide', 'Lumineux', 'Parfait'
    ];
    
    const List<String> nouns = [
      'Lion', 'Aigle', 'Dragon', 'Phoenix', 'Tigre',
      'Falcon', 'Requin', 'Loup', 'Ours', 'Leopard'
    ];
    
    final Random random = Random.secure();
    final adjective = adjectives[random.nextInt(adjectives.length)];
    final noun = nouns[random.nextInt(nouns.length)];
    final number = random.nextInt(100);
    final symbol = ['!', '@', '#', '\$', '%'][random.nextInt(5)];
    
    return '$adjective$noun$number$symbol';
  }
}
