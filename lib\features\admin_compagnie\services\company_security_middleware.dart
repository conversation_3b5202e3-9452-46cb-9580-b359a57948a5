import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// 🛡️ Middleware de sécurité pour empêcher l'accès croisé entre compagnies
/// 
/// Ce service garantit que les admin_compagnie ne peuvent accéder qu'aux
/// données de leur propre compagnie et empêche tout accès non autorisé
class CompanySecurityMiddleware {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🔐 Vérifier l'accès à une ressource spécifique d'une compagnie
  static Future<SecurityCheckResult> checkCompanyResourceAccess({
    required String compagnieId,
    required String resourceType,
    String? resourceId,
    Map<String, dynamic>? additionalContext,
  }) async {
    try {
      debugPrint('[COMPANY_SECURITY] 🔍 Vérification accès ressource:');
      debugPrint('[COMPANY_SECURITY]   - CompagnieId: $compagnieId');
      debugPrint('[COMPANY_SECURITY]   - ResourceType: $resourceType');
      debugPrint('[COMPANY_SECURITY]   - ResourceId: $resourceId');

      // 1. Vérifier l'utilisateur connecté
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return SecurityCheckResult.denied(
          reason: 'Utilisateur non authentifié',
          action: 'Se connecter',
        );
      }

      // 2. Récupérer les données utilisateur
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        return SecurityCheckResult.denied(
          reason: 'Compte utilisateur non trouvé',
          action: 'Contacter l\'administrateur',
        );
      }

      final userData = userDoc.data()!;
      final userRole = userData['role'] as String?;
      final userCompagnieId = userData['compagnieId'] as String?;

      // 3. Vérifications selon le rôle
      switch (userRole) {
        case 'super_admin':
          // Super admin a accès à tout
          debugPrint('[COMPANY_SECURITY] ✅ Accès Super Admin autorisé');
          return SecurityCheckResult.granted(
            userRole: userRole ?? 'super_admin',
            userId: currentUser.uid,
            userEmail: currentUser.email ?? '',
          );

        case 'admin_compagnie':
          // Admin compagnie ne peut accéder qu'à sa propre compagnie
          if (userCompagnieId != compagnieId) {
            await _logSecurityViolation(
              userId: currentUser.uid,
              userEmail: currentUser.email ?? '',
              attemptedCompagnieId: compagnieId,
              userCompagnieId: userCompagnieId ?? 'null',
              resourceType: resourceType,
              resourceId: resourceId,
              violation: 'cross_company_access_attempt',
            );

            return SecurityCheckResult.denied(
              reason: 'Accès refusé: Vous ne pouvez accéder qu\'aux données de votre compagnie',
              action: 'Contacter votre administrateur',
              currentRole: userRole,
            );
          }

          debugPrint('[COMPANY_SECURITY] ✅ Accès Admin Compagnie autorisé');
          return SecurityCheckResult.granted(
            userRole: userRole ?? 'admin_compagnie',
            userId: currentUser.uid,
            userEmail: currentUser.email ?? '',
          );

        default:
          return SecurityCheckResult.denied(
            reason: 'Rôle non autorisé pour cette ressource',
            action: 'Contacter l\'administrateur',
            currentRole: userRole ?? 'unknown',
          );
      }

    } catch (e) {
      debugPrint('[COMPANY_SECURITY] ❌ Erreur vérification sécurité: $e');
      return SecurityCheckResult.error(
        error: 'Erreur système lors de la vérification de sécurité',
      );
    }
  }

  /// 🔍 Vérifier l'accès à une collection Firestore spécifique à une compagnie
  static Future<bool> canAccessFirestoreCollection({
    required String compagnieId,
    required String collection,
    String? documentId,
  }) async {
    final result = await checkCompanyResourceAccess(
      compagnieId: compagnieId,
      resourceType: 'firestore_collection',
      resourceId: documentId,
      additionalContext: {'collection': collection},
    );

    return result.isGranted;
  }

  /// 👥 Vérifier l'accès aux données d'agents d'une compagnie
  static Future<bool> canAccessAgentData(String compagnieId, String agentId) async {
    final result = await checkCompanyResourceAccess(
      compagnieId: compagnieId,
      resourceType: 'agent_data',
      resourceId: agentId,
    );

    return result.isGranted;
  }

  /// 📄 Vérifier l'accès aux contrats d'une compagnie
  static Future<bool> canAccessContractData(String compagnieId, String contractId) async {
    final result = await checkCompanyResourceAccess(
      compagnieId: compagnieId,
      resourceType: 'contract_data',
      resourceId: contractId,
    );

    return result.isGranted;
  }

  /// 🚗 Vérifier l'accès aux sinistres d'une compagnie
  static Future<bool> canAccessClaimData(String compagnieId, String claimId) async {
    final result = await checkCompanyResourceAccess(
      compagnieId: compagnieId,
      resourceType: 'claim_data',
      resourceId: claimId,
    );

    return result.isGranted;
  }

  /// 🏢 Vérifier l'accès aux agences d'une compagnie
  static Future<bool> canAccessAgencyData(String compagnieId, String agencyId) async {
    final result = await checkCompanyResourceAccess(
      compagnieId: compagnieId,
      resourceType: 'agency_data',
      resourceId: agencyId,
    );

    return result.isGranted;
  }

  /// 📊 Créer une requête Firestore sécurisée pour une compagnie
  static Future<Query<Map<String, dynamic>>?> createSecureCompanyQuery({
    required String collection,
    required String compagnieId,
  }) async {
    final hasAccess = await canAccessFirestoreCollection(
      compagnieId: compagnieId,
      collection: collection,
    );

    if (!hasAccess) {
      debugPrint('[COMPANY_SECURITY] ❌ Accès refusé à la collection: $collection');
      return null;
    }

    // Créer une requête filtrée par compagnieId
    return _firestore
        .collection(collection)
        .where('compagnieId', isEqualTo: compagnieId);
  }

  /// 📝 Logger les violations de sécurité
  static Future<void> _logSecurityViolation({
    required String userId,
    required String userEmail,
    required String attemptedCompagnieId,
    required String userCompagnieId,
    required String resourceType,
    String? resourceId,
    required String violation,
  }) async {
    try {
      await _firestore.collection('security_violations').add({
        'userId': userId,
        'userEmail': userEmail,
        'userCompagnieId': userCompagnieId,
        'attemptedCompagnieId': attemptedCompagnieId,
        'resourceType': resourceType,
        'resourceId': resourceId,
        'violation': violation,
        'timestamp': FieldValue.serverTimestamp(),
        'severity': 'HIGH',
        'status': 'active',
        'userAgent': 'Flutter App',
        'ipAddress': 'N/A', // TODO: Récupérer l'IP si possible
      });

      debugPrint('[COMPANY_SECURITY] 🚨 Violation de sécurité loggée: $violation');

    } catch (e) {
      debugPrint('[COMPANY_SECURITY] ❌ Erreur logging violation: $e');
    }
  }

  /// 🔍 Obtenir les violations de sécurité récentes (Super Admin uniquement)
  static Future<List<Map<String, dynamic>>> getRecentSecurityViolations({
    int limit = 50,
  }) async {
    try {
      // Vérifier que l'utilisateur est Super Admin
      final currentUser = _auth.currentUser;
      if (currentUser == null) return [];

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists || userDoc.data()!['role'] != 'super_admin') {
        debugPrint('[COMPANY_SECURITY] ❌ Accès refusé aux logs de sécurité');
        return [];
      }

      final snapshot = await _firestore
          .collection('security_violations')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();

    } catch (e) {
      debugPrint('[COMPANY_SECURITY] ❌ Erreur récupération violations: $e');
      return [];
    }
  }

  /// 🛡️ Middleware pour les routes protégées
  static Future<bool> validateRouteAccess({
    required String route,
    Map<String, String>? routeParams,
  }) async {
    try {
      // Extraire le compagnieId de la route si présent
      String? compagnieId;
      
      if (route.contains('/compagnie-dashboard/')) {
        compagnieId = route.split('/compagnie-dashboard/').last;
      } else if (routeParams != null && routeParams.containsKey('compagnieId')) {
        compagnieId = routeParams['compagnieId'];
      }

      if (compagnieId == null) {
        debugPrint('[COMPANY_SECURITY] ⚠️ Route sans compagnieId: $route');
        return true; // Laisser passer les routes génériques
      }

      final result = await checkCompanyResourceAccess(
        compagnieId: compagnieId,
        resourceType: 'route_access',
        additionalContext: {'route': route},
      );

      return result.isGranted;

    } catch (e) {
      debugPrint('[COMPANY_SECURITY] ❌ Erreur validation route: $e');
      return false;
    }
  }
}

/// 📋 Résultat d'une vérification de sécurité
class SecurityCheckResult {
  final bool isGranted;
  final String? reason;
  final String? action;
  final String? currentRole;
  final String? userRole;
  final String? userId;
  final String? userEmail;
  final String? error;

  SecurityCheckResult._({
    required this.isGranted,
    this.reason,
    this.action,
    this.currentRole,
    this.userRole,
    this.userId,
    this.userEmail,
    this.error,
  });

  factory SecurityCheckResult.granted({
    required String userRole,
    required String userId,
    required String userEmail,
  }) {
    return SecurityCheckResult._(
      isGranted: true,
      userRole: userRole,
      userId: userId,
      userEmail: userEmail,
    );
  }

  factory SecurityCheckResult.denied({
    required String reason,
    required String action,
    String? currentRole,
  }) {
    return SecurityCheckResult._(
      isGranted: false,
      reason: reason,
      action: action,
      currentRole: currentRole,
    );
  }

  factory SecurityCheckResult.error({
    required String error,
  }) {
    return SecurityCheckResult._(
      isGranted: false,
      error: error,
    );
  }
}
