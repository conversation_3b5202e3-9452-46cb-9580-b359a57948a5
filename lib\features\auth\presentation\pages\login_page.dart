import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../../../core/services/role_navigation_service.dart';
import '../../../../core/services/offline_admin_service.dart';

/// 🔐 Page de connexion
/// 
/// Gère l'authentification et redirige vers l'interface appropriée selon le rôle
class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              _buildHeader(),
              const SizedBox(height: 40),
              _buildForm(),
              const SizedBox(height: 24),
              _buildLoginButton(),
              const SizedBox(height: 16),
              _buildForgotPassword(),
            ],
          ),
        ),
      ),
    );
  }

  /// 📋 Construire l'en-tête
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [ModernTheme.primaryColor, ModernTheme.accentColor],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: const Icon(
            Icons.directions_car,
            color: Colors.white,
            size: 48,
          ),
        ),
        const SizedBox(height: 24),
        Text(
          'Bienvenue',
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: ModernTheme.textColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Connectez-vous à votre espace Constat Tunisie',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 📝 Construire le formulaire
  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          TextFormField(
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              labelText: 'Email',
              prefixIcon: const Icon(Icons.email),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Veuillez saisir votre email';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Veuillez saisir un email valide';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Mot de passe',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility : Icons.visibility_off,
                ),
                onPressed: () {
                  setState(() => _obscurePassword = !_obscurePassword);
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Veuillez saisir votre mot de passe';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 🔄 Construire le bouton de connexion
  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _login,
        style: ElevatedButton.styleFrom(
          backgroundColor: ModernTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text(
                'Se connecter',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  /// 🔗 Construire le lien mot de passe oublié
  Widget _buildForgotPassword() {
    return Center(
      child: TextButton(
        onPressed: () {
          // TODO: Implémenter la récupération de mot de passe
        },
        child: Text(
          'Mot de passe oublié ?',
          style: TextStyle(
            color: ModernTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 🔐 Connexion
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      debugPrint('[LOGIN] 🔐 Tentative de connexion: $email');

      // 1. Essayer d'abord la connexion Firebase normale
      UserCredential? userCredential;
      
      try {
        userCredential = await _auth.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
        debugPrint('[LOGIN] ✅ Connexion Firebase réussie');
      } catch (e) {
        debugPrint('[LOGIN] ⚠️ Connexion Firebase échouée: $e');
        
        // 2. Fallback : Vérifier les comptes hors ligne
        final offlineResult = await OfflineAdminService.validateAdminLogin(
          email: email,
          password: password,
        );

        if (offlineResult['success']) {
          debugPrint('[LOGIN] ✅ Connexion hors ligne réussie');
          
          // Afficher un message d'information
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Connexion en mode hors ligne - Synchronisation en attente'),
                backgroundColor: Colors.orange,
              ),
            );
          }

          // TODO: Rediriger vers l'interface appropriée pour les comptes hors ligne
          // Pour l'instant, on affiche juste un message
          _showOfflineAccountDialog(offlineResult['admin']);
          return;
        } else {
          // Aucune connexion possible
          throw Exception('Email ou mot de passe incorrect');
        }
      }

      // 3. Connexion Firebase réussie - Rediriger selon le rôle
      if (userCredential != null && mounted) {
        debugPrint('[LOGIN] 🧭 Redirection selon le rôle...');
        await RoleNavigationService.navigateToRoleInterface(context);
      }

    } catch (e) {
      debugPrint('[LOGIN] ❌ Erreur connexion: $e');
      
      if (mounted) {
        String errorMessage = 'Erreur de connexion';
        
        if (e.toString().contains('user-not-found')) {
          errorMessage = 'Aucun compte trouvé avec cet email';
        } else if (e.toString().contains('wrong-password')) {
          errorMessage = 'Mot de passe incorrect';
        } else if (e.toString().contains('invalid-email')) {
          errorMessage = 'Format d\'email invalide';
        } else if (e.toString().contains('user-disabled')) {
          errorMessage = 'Ce compte a été désactivé';
        } else if (e.toString().contains('too-many-requests')) {
          errorMessage = 'Trop de tentatives. Réessayez plus tard';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 📱 Afficher dialog pour compte hors ligne
  void _showOfflineAccountDialog(Map<String, dynamic> adminData) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.cloud_off, color: Colors.orange),
            SizedBox(width: 8),
            Text('Compte hors ligne'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Bienvenue ${adminData['prenom']} ${adminData['nom']} !'),
            const SizedBox(height: 8),
            Text('Rôle: ${adminData['role']}'),
            Text('Compagnie: ${adminData['compagnieNom']}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Votre compte a été créé hors ligne. Il sera synchronisé avec Firebase dès que la connexion sera rétablie.',
                style: TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Rediriger vers l'interface appropriée
            },
            child: const Text('Continuer'),
          ),
        ],
      ),
    );
  }
}
