import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/user_management_service_original.dart';
import '../../services/user_role_validation_service_complete.dart';
import '../widgets/user_management_widgets.dart';

/// 👥 Écran de gestion des utilisateurs pour Super Admin
class UserManagementScreenOriginal extends StatefulWidget {
  const UserManagementScreenOriginal({super.key});

  @override
  State<UserManagementScreenOriginal> createState() => _UserManagementScreenOriginalState();
}

class _UserManagementScreenOriginalState extends State<UserManagementScreenOriginal> {
  final ScrollController _scrollController = ScrollController();
  
  List<Map<String, dynamic>> _users = [];
  bool _isLoading = false;
  bool _hasMore = true;
  DocumentSnapshot? _lastDocument;
  
  // Filtres
  String _roleFilter = 'tous';
  String _statusFilter = 'tous';
  String _compagnieFilter = 'toutes';
  String _agenceFilter = 'toutes';
  
  // Données pour les dropdowns
  List<Map<String, dynamic>> _compagnies = [];
  List<Map<String, dynamic>> _agences = [];

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// 📊 Charger les données initiales
  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadUsers(refresh: true),
      _loadCompagnies(),
      _loadAgences(),
    ]);
  }

  /// 👥 Charger les utilisateurs
  Future<void> _loadUsers({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      if (refresh) {
        _users.clear();
        _lastDocument = null;
        _hasMore = true;
      }
    });

    try {
      final result = await UserManagementService.getAllUsers(
        limit: 20,
        lastDocument: _lastDocument,
        roleFilter: _roleFilter,
        statusFilter: _statusFilter,
        compagnieFilter: _compagnieFilter,
        agenceFilter: _agenceFilter,
      );

      if (mounted) {
        setState(() {
          if (refresh) {
            _users = result['users'];
          } else {
            _users.addAll(result['users']);
          }
          _lastDocument = result['lastDocument'];
          _hasMore = result['hasMore'];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar('Erreur chargement utilisateurs: $e');
      }
    }
  }

  /// 🏢 Charger les compagnies
  Future<void> _loadCompagnies() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('compagnies_assurance')
          .orderBy('nom')
          .get();

      if (mounted) {
        setState(() {
          _compagnies = snapshot.docs.map((doc) => {
            'id': doc.id,
            'nom': doc.data()['nom'] ?? 'Sans nom',
          }).toList();
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement compagnies: $e');
    }
  }

  /// 🏛️ Charger les agences
  Future<void> _loadAgences() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('agences_assurance')
          .orderBy('nom')
          .get();

      if (mounted) {
        setState(() {
          _agences = snapshot.docs.map((doc) => {
            'id': doc.id,
            'nom': doc.data()['nom'] ?? 'Sans nom',
            'compagnieId': doc.data()['compagnieId'],
          }).toList();
        });
      }
    } catch (e) {
      debugPrint('Erreur chargement agences: $e');
    }
  }

  /// 📜 Gestion du scroll pour pagination
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (_hasMore && !_isLoading) {
        _loadUsers();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Gestion des Utilisateurs'),
        backgroundColor: ModernTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => _loadUsers(refresh: true),
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser',
          ),
          IconButton(
            onPressed: _showCreateUserDialog,
            icon: const Icon(Icons.person_add),
            tooltip: 'Créer un utilisateur',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtres
          _buildFiltersSection(),
          
          // Liste des utilisateurs
          Expanded(
            child: _buildUsersList(),
          ),
        ],
      ),
    );
  }

  /// 🔍 Section des filtres
  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filtres',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              // Filtre par rôle
              SizedBox(
                width: 200,
                child: DropdownButtonFormField<String>(
                  value: _roleFilter,
                  decoration: const InputDecoration(
                    labelText: 'Rôle',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem(value: 'tous', child: Text('Tous les rôles')),
                    ...UserRoleValidationServiceComplete.availableRoles.entries.map(
                      (entry) => DropdownMenuItem(
                        value: entry.key,
                        child: Text(entry.value),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() => _roleFilter = value!);
                    _loadUsers(refresh: true);
                  },
                ),
              ),
              
              // Filtre par statut
              SizedBox(
                width: 150,
                child: DropdownButtonFormField<String>(
                  value: _statusFilter,
                  decoration: const InputDecoration(
                    labelText: 'Statut',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'tous', child: Text('Tous')),
                    DropdownMenuItem(value: 'actif', child: Text('Actif')),
                    DropdownMenuItem(value: 'inactif', child: Text('Inactif')),
                    DropdownMenuItem(value: 'suspendu', child: Text('Suspendu')),
                  ],
                  onChanged: (value) {
                    setState(() => _statusFilter = value!);
                    _loadUsers(refresh: true);
                  },
                ),
              ),
              
              // Filtre par compagnie
              SizedBox(
                width: 200,
                child: DropdownButtonFormField<String>(
                  value: _compagnieFilter,
                  decoration: const InputDecoration(
                    labelText: 'Compagnie',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: [
                    const DropdownMenuItem(value: 'toutes', child: Text('Toutes')),
                    ..._compagnies.map(
                      (compagnie) => DropdownMenuItem(
                        value: compagnie['id'],
                        child: Text(compagnie['nom']),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() => _compagnieFilter = value!);
                    _loadUsers(refresh: true);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 👥 Liste des utilisateurs
  Widget _buildUsersList() {
    if (_users.isEmpty && _isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_users.isEmpty) {
      return const Center(
        child: Text(
          'Aucun utilisateur trouvé',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _users.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _users.length) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        final user = _users[index];
        return UserCard(
          user: user,
          onEdit: () => _showEditUserDialog(user),
          onDelete: () => _showDeleteUserDialog(user),
          onResetPassword: () => _resetUserPassword(user),
        );
      },
    );
  }

  /// ➕ Afficher le dialogue de création d'utilisateur
  void _showCreateUserDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateUserDialog(
        compagnies: _compagnies,
        agences: _agences,
        onUserCreated: () => _loadUsers(refresh: true),
      ),
    );
  }

  /// ✏️ Afficher le dialogue d'édition d'utilisateur
  void _showEditUserDialog(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => EditUserDialog(
        user: user,
        compagnies: _compagnies,
        agences: _agences,
        onUserUpdated: () => _loadUsers(refresh: true),
      ),
    );
  }

  /// 🗑️ Afficher le dialogue de suppression d'utilisateur
  void _showDeleteUserDialog(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer l\'utilisateur'),
        content: Text(
          'Êtes-vous sûr de vouloir supprimer l\'utilisateur ${user['prenom']} ${user['nom']} ?\n\n'
          'Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteUser(user['uid']);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }

  /// 🗑️ Supprimer un utilisateur
  Future<void> _deleteUser(String uid) async {
    try {
      final result = await UserManagementService.deleteUser(uid);
      
      if (result['success']) {
        _showSuccessSnackBar('Utilisateur supprimé avec succès');
        _loadUsers(refresh: true);
      } else {
        _showErrorSnackBar('Erreur: ${result['error']}');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur suppression: $e');
    }
  }

  /// 🔄 Réinitialiser le mot de passe d'un utilisateur
  Future<void> _resetUserPassword(Map<String, dynamic> user) async {
    try {
      final result = await UserManagementService.resetUserPassword(user['uid']);
      
      if (result['success']) {
        _showSuccessSnackBar(
          'Mot de passe réinitialisé pour ${user['prenom']} ${user['nom']}'
        );
      } else {
        _showErrorSnackBar('Erreur: ${result['error']}');
      }
    } catch (e) {
      _showErrorSnackBar('Erreur réinitialisation: $e');
    }
  }

  /// ✅ Afficher un message de succès
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// ❌ Afficher un message d'erreur
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
