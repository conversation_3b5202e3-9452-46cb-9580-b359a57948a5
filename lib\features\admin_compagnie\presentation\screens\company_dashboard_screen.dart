import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/company_redirect_service.dart';

/// 🏢 Dashboard spécifique pour chaque compagnie d'assurance
/// 
/// Ce dashboard s'adapte dynamiquement à la compagnie de l'admin connecté
/// et affiche uniquement les données de sa compagnie
class CompanyDashboardScreen extends StatefulWidget {
  final String compagnieId;

  const CompanyDashboardScreen({
    super.key,
    required this.compagnieId,
  });

  @override
  State<CompanyDashboardScreen> createState() => _CompanyDashboardScreenState();
}

class _CompanyDashboardScreenState extends State<CompanyDashboardScreen> {
  Map<String, dynamic>? _companyInfo;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadCompanyInfo();
    _validateAccess();
  }

  /// 🔐 Valider l'accès à cette compagnie
  Future<void> _validateAccess() async {
    final hasAccess = await CompanyRedirectService.canAccessCompany(widget.compagnieId);
    
    if (!hasAccess) {
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  /// 📊 Charger les informations de la compagnie
  Future<void> _loadCompanyInfo() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      _companyInfo = await CompanyRedirectService.getCurrentUserCompanyInfo();

      if (_companyInfo == null) {
        setState(() {
          _error = 'Impossible de charger les informations de la compagnie';
        });
        return;
      }

      setState(() {
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _error = 'Erreur: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Erreur'),
          backgroundColor: Colors.red,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                _error!,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCompanyInfo,
                child: const Text('Réessayer'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  /// 📱 Construire l'AppBar personnalisée
  PreferredSizeWidget _buildAppBar() {
    final companyName = _companyInfo?['nom'] ?? 'Compagnie';
    final companyColor = _companyInfo?['couleur'] != null 
        ? Color(int.parse(_companyInfo!['couleur'].replaceFirst('#', '0xFF')))
        : Colors.blue;

    return AppBar(
      title: Row(
        children: [
          if (_companyInfo?['logo'] != null)
            CircleAvatar(
              radius: 16,
              backgroundImage: NetworkImage(_companyInfo!['logo']),
            ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  companyName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Dashboard Admin',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: companyColor,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            // TODO: Notifications
          },
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () {
            // TODO: Paramètres
          },
        ),
      ],
    );
  }

  /// 🎯 Construire le drawer de navigation
  Widget _buildDrawer() {
    final userName = '${_companyInfo?['userPrenom']} ${_companyInfo?['userNom']}';
    final userEmail = _companyInfo?['userEmail'];
    final companyName = _companyInfo?['nom'];

    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(userName),
            accountEmail: Text(userEmail ?? ''),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                userName.isNotEmpty ? userName[0].toUpperCase() : 'A',
                style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
            ),
            decoration: BoxDecoration(
              color: _companyInfo?['couleur'] != null 
                  ? Color(int.parse(_companyInfo!['couleur'].replaceFirst('#', '0xFF')))
                  : Colors.blue,
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Tableau de Bord'),
            selected: true,
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('Gestion des Agents'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigation vers gestion agents
            },
          ),
          ListTile(
            leading: const Icon(Icons.business),
            title: const Text('Agences'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigation vers gestion agences
            },
          ),
          ListTile(
            leading: const Icon(Icons.description),
            title: const Text('Contrats'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigation vers gestion contrats
            },
          ),
          ListTile(
            leading: const Icon(Icons.car_crash),
            title: const Text('Sinistres'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigation vers gestion sinistres
            },
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Rapports'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigation vers rapports
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Déconnexion', style: TextStyle(color: Colors.red)),
            onTap: () {
              _logout();
            },
          ),
        ],
      ),
    );
  }

  /// 📊 Construire le corps du dashboard
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 16),
          _buildStatsGrid(),
          const SizedBox(height: 16),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  /// 👋 Carte de bienvenue
  Widget _buildWelcomeCard() {
    final userName = '${_companyInfo?['userPrenom']} ${_companyInfo?['userNom']}';
    final companyName = _companyInfo?['nom'];

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.waving_hand, size: 32, color: Colors.orange),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bienvenue, $userName !',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Administrateur de $companyName',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 📈 Grille de statistiques
  Widget _buildStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildStatCard(
          title: 'Agents Actifs',
          value: '0', // TODO: Données réelles
          icon: Icons.people,
          color: Colors.blue,
        ),
        _buildStatCard(
          title: 'Contrats',
          value: '0', // TODO: Données réelles
          icon: Icons.description,
          color: Colors.green,
        ),
        _buildStatCard(
          title: 'Sinistres',
          value: '0', // TODO: Données réelles
          icon: Icons.car_crash,
          color: Colors.orange,
        ),
        _buildStatCard(
          title: 'Agences',
          value: '0', // TODO: Données réelles
          icon: Icons.business,
          color: Colors.purple,
        ),
      ],
    );
  }

  /// 📊 Carte de statistique
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Activité récente
  Widget _buildRecentActivity() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Activité Récente',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Center(
              child: Text(
                'Aucune activité récente',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🚪 Déconnexion
  void _logout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Déconnexion'),
        content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushReplacementNamed(context, '/login');
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Déconnexion'),
          ),
        ],
      ),
    );
  }
}
