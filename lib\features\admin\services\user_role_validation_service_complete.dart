import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 🔐 Service complet de validation des rôles utilisateurs
class UserRoleValidationServiceComplete {
  
  /// 📋 Rôles disponibles dans le système
  static const Map<String, String> availableRoles = {
    'super_admin': 'Super Administrateur',
    'admin_compagnie': 'Administrateur Compagnie',
    'admin_agence': 'Administrateur Agence',
    'agent': 'Agent',
    'conducteur': 'Conducteur',
    'expert': 'Expert',
  };

  /// 🏢 Rôles qui nécessitent une compagnie
  static const List<String> rolesRequiringCompagnie = [
    'admin_compagnie',
    'admin_agence',
    'agent',
  ];

  /// 🏛️ Rôles qui nécessitent une agence
  static const List<String> rolesRequiringAgence = [
    'admin_agence',
    'agent',
  ];

  /// ✅ Valider un rôle
  static bool isValidRole(String role) {
    return availableRoles.contains<PERSON>ey(role);
  }

  /// 🏢 Vérifier si un rôle nécessite une compagnie
  static bool requiresCompagnie(String role) {
    return rolesRequiringCompagnie.contains(role);
  }

  /// 🏛️ Vérifier si un rôle nécessite une agence
  static bool requiresAgence(String role) {
    return rolesRequiringAgence.contains(role);
  }

  /// 📊 Valider les données d'un utilisateur
  static Map<String, dynamic> validateUserData({
    required String email,
    required String nom,
    required String prenom,
    required String role,
    String? compagnieId,
    String? agenceId,
    String? phone,
  }) {
    final errors = <String>[];

    // Validation email
    if (email.isEmpty || !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      errors.add('Email invalide');
    }

    // Validation nom/prénom
    if (nom.isEmpty || nom.length < 2) {
      errors.add('Nom requis (minimum 2 caractères)');
    }
    if (prenom.isEmpty || prenom.length < 2) {
      errors.add('Prénom requis (minimum 2 caractères)');
    }

    // Validation rôle
    if (!isValidRole(role)) {
      errors.add('Rôle invalide');
    }

    // Validation compagnie si nécessaire
    if (requiresCompagnie(role) && (compagnieId == null || compagnieId.isEmpty)) {
      errors.add('Compagnie requise pour ce rôle');
    }

    // Validation agence si nécessaire
    if (requiresAgence(role) && (agenceId == null || agenceId.isEmpty)) {
      errors.add('Agence requise pour ce rôle');
    }

    // Validation téléphone si fourni
    if (phone != null && phone.isNotEmpty) {
      if (!RegExp(r'^\+?[0-9]{8,15}$').hasMatch(phone.replaceAll(' ', ''))) {
        errors.add('Numéro de téléphone invalide');
      }
    }

    return {
      'isValid': errors.isEmpty,
      'errors': errors,
    };
  }

  /// 🎨 Obtenir la couleur d'un rôle
  static Color getRoleColor(String role) {
    switch (role) {
      case 'super_admin':
        return Colors.purple;
      case 'admin_compagnie':
        return Colors.blue;
      case 'admin_agence':
        return Colors.green;
      case 'agent':
        return Colors.orange;
      case 'conducteur':
        return Colors.teal;
      case 'expert':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 🏷️ Obtenir l'icône d'un rôle
  static IconData getRoleIcon(String role) {
    switch (role) {
      case 'super_admin':
        return Icons.admin_panel_settings;
      case 'admin_compagnie':
        return Icons.business;
      case 'admin_agence':
        return Icons.location_city;
      case 'agent':
        return Icons.person_pin;
      case 'conducteur':
        return Icons.drive_eta;
      case 'expert':
        return Icons.engineering;
      default:
        return Icons.person;
    }
  }

  /// 📊 Obtenir les informations détaillées d'un rôle
  static Map<String, String> getRoleDescription(String role) {
    switch (role) {
      case 'super_admin':
        return {
          'title': 'Super Administrateur',
          'description': 'Accès complet au système',
          'permissions': 'Toutes les permissions',
        };
      case 'admin_compagnie':
        return {
          'title': 'Administrateur Compagnie',
          'description': 'Gestion de la compagnie d\'assurance',
          'permissions': 'Gestion agences, agents, contrats',
        };
      case 'admin_agence':
        return {
          'title': 'Administrateur Agence',
          'description': 'Gestion d\'une agence spécifique',
          'permissions': 'Gestion agents, contrats de l\'agence',
        };
      case 'agent':
        return {
          'title': 'Agent',
          'description': 'Agent d\'assurance',
          'permissions': 'Gestion contrats, constats',
        };
      case 'conducteur':
        return {
          'title': 'Conducteur',
          'description': 'Utilisateur final',
          'permissions': 'Déclaration accidents, consultation contrats',
        };
      case 'expert':
        return {
          'title': 'Expert',
          'description': 'Expert automobile',
          'permissions': 'Expertise véhicules, rapports',
        };
      default:
        return {
          'title': 'Utilisateur',
          'description': 'Rôle non défini',
          'permissions': 'Aucune permission',
        };
    }
  }

  /// 🔒 Vérifier les permissions
  static bool hasPermission(String userRole, String requiredPermission) {
    // Super admin a toutes les permissions
    if (userRole == 'super_admin') return true;

    switch (requiredPermission) {
      case 'create_users':
        return ['super_admin'].contains(userRole);
      case 'manage_compagnies':
        return ['super_admin'].contains(userRole);
      case 'manage_agences':
        return ['super_admin', 'admin_compagnie'].contains(userRole);
      case 'manage_agents':
        return ['super_admin', 'admin_compagnie', 'admin_agence'].contains(userRole);
      case 'view_reports':
        return ['super_admin', 'admin_compagnie', 'admin_agence'].contains(userRole);
      case 'manage_contracts':
        return ['super_admin', 'admin_compagnie', 'admin_agence', 'agent'].contains(userRole);
      case 'create_expertise':
        return ['expert'].contains(userRole);
      case 'declare_accident':
        return ['conducteur'].contains(userRole);
      default:
        return false;
    }
  }

  /// 🔍 Valider l'existence des entités liées
  static Future<Map<String, dynamic>> validateRelatedEntities({
    String? compagnieId,
    String? agenceId,
  }) async {
    final errors = <String>[];

    try {
      // Vérifier la compagnie si fournie
      if (compagnieId != null && compagnieId.isNotEmpty) {
        final compagnieDoc = await FirebaseFirestore.instance
            .collection('compagnies_assurance')
            .doc(compagnieId)
            .get();
        
        if (!compagnieDoc.exists) {
          errors.add('Compagnie non trouvée');
        }
      }

      // Vérifier l'agence si fournie
      if (agenceId != null && agenceId.isNotEmpty) {
        final agenceDoc = await FirebaseFirestore.instance
            .collection('agences_assurance')
            .doc(agenceId)
            .get();
        
        if (!agenceDoc.exists) {
          errors.add('Agence non trouvée');
        }
      }

      return {
        'isValid': errors.isEmpty,
        'errors': errors,
      };

    } catch (e) {
      return {
        'isValid': false,
        'errors': ['Erreur validation entités: $e'],
      };
    }
  }

  /// 📋 Obtenir les rôles disponibles pour un utilisateur
  static List<String> getAvailableRolesForUser(String currentUserRole) {
    switch (currentUserRole) {
      case 'super_admin':
        return availableRoles.keys.toList();
      case 'admin_compagnie':
        return ['admin_agence', 'agent'];
      case 'admin_agence':
        return ['agent'];
      default:
        return [];
    }
  }

  /// 🎯 Obtenir les champs requis pour un rôle
  static List<String> getRequiredFieldsForRole(String role) {
    final baseFields = ['email', 'nom', 'prenom'];
    
    switch (role) {
      case 'admin_compagnie':
        return [...baseFields, 'compagnieId'];
      case 'admin_agence':
      case 'agent':
        return [...baseFields, 'compagnieId', 'agenceId'];
      case 'expert':
        return [...baseFields, 'phone'];
      default:
        return baseFields;
    }
  }

  /// 🔄 Valider la transition de rôle
  static bool canChangeRole(String fromRole, String toRole, String currentUserRole) {
    // Seul le super admin peut changer n'importe quel rôle
    if (currentUserRole == 'super_admin') return true;

    // Admin compagnie peut gérer les rôles inférieurs
    if (currentUserRole == 'admin_compagnie') {
      return ['admin_agence', 'agent'].contains(toRole);
    }

    // Admin agence peut gérer les agents
    if (currentUserRole == 'admin_agence') {
      return toRole == 'agent';
    }

    return false;
  }

  /// 📊 Obtenir les statistiques des rôles
  static Future<Map<String, int>> getRoleStatistics() async {
    try {
      final stats = <String, int>{};
      
      for (final role in availableRoles.keys) {
        final count = await FirebaseFirestore.instance
            .collection('users')
            .where('role', isEqualTo: role)
            .where('status', isEqualTo: 'actif')
            .get();
        
        stats[role] = count.docs.length;
      }

      return stats;
    } catch (e) {
      debugPrint('Erreur récupération statistiques rôles: $e');
      return {};
    }
  }
}
