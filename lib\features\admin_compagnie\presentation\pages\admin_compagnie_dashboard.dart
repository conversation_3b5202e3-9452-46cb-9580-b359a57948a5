import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';

/// 🏢 Dashboard Admin Compagnie
/// 
/// Interface spécifique pour les administrateurs de compagnie d'assurance
/// Permet de gérer les agences, agents, contrats et sinistres de la compagnie
class AdminCompagnieDashboard extends StatefulWidget {
  final String compagnieId;

  const AdminCompagnieDashboard({
    Key? key,
    required this.compagnieId,
  }) : super(key: key);

  @override
  State<AdminCompagnieDashboard> createState() => _AdminCompagnieDashboardState();
}

class _AdminCompagnieDashboardState extends State<AdminCompagnieDashboard> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Map<String, dynamic>? _compagnieData;
  Map<String, dynamic>? _userData;
  bool _isLoading = true;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 📊 Charger les données de la compagnie et de l'utilisateur
  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Charger les données de la compagnie
      final compagnieDoc = await _firestore
          .collection('compagnies_assurance')
          .doc(widget.compagnieId)
          .get();

      if (compagnieDoc.exists) {
        _compagnieData = compagnieDoc.data();
      }

      // Charger les données de l'utilisateur
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        final userDoc = await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .get();

        if (userDoc.exists) {
          _userData = userDoc.data();
        }
      }

    } catch (e) {
      debugPrint('[ADMIN_COMPAGNIE] ❌ Erreur chargement données: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  /// 📱 Construire l'AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Icon(Icons.business, color: ModernTheme.primaryColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _compagnieData?['nom'] ?? 'Compagnie',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Admin Dashboard',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      iconTheme: IconThemeData(color: ModernTheme.primaryColor),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            // TODO: Afficher les notifications
          },
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'profile':
                _showProfile();
                break;
              case 'settings':
                _showSettings();
                break;
              case 'logout':
                _logout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person),
                  SizedBox(width: 8),
                  Text('Profil'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('Paramètres'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Déconnexion', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 🗂️ Construire le Drawer
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [ModernTheme.primaryColor, ModernTheme.accentColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Text(
                    (_userData?['prenom']?[0] ?? 'A').toUpperCase(),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ModernTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_userData?['prenom'] ?? ''} ${_userData?['nom'] ?? ''}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Admin Compagnie',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.dashboard,
                  title: 'Tableau de bord',
                  index: 0,
                ),
                _buildDrawerItem(
                  icon: Icons.store,
                  title: 'Gestion Agences',
                  index: 1,
                ),
                _buildDrawerItem(
                  icon: Icons.people,
                  title: 'Gestion Agents',
                  index: 2,
                ),
                _buildDrawerItem(
                  icon: Icons.description,
                  title: 'Contrats',
                  index: 3,
                ),
                _buildDrawerItem(
                  icon: Icons.report_problem,
                  title: 'Sinistres',
                  index: 4,
                ),
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Statistiques',
                  index: 5,
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'Paramètres',
                  index: 6,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Construire un élément du drawer
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? ModernTheme.primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? ModernTheme.primaryColor : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: ModernTheme.primaryColor.withOpacity(0.1),
      onTap: () {
        setState(() => _selectedIndex = index);
        Navigator.of(context).pop();
      },
    );
  }

  /// 📱 Construire le corps de la page
  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardView();
      case 1:
        return _buildAgencesView();
      case 2:
        return _buildAgentsView();
      case 3:
        return _buildContratsView();
      case 4:
        return _buildSinistresView();
      case 5:
        return _buildStatistiquesView();
      case 6:
        return _buildParametresView();
      default:
        return _buildDashboardView();
    }
  }

  /// 📊 Vue tableau de bord
  Widget _buildDashboardView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tableau de bord',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildStatCard(
                  title: 'Agences',
                  value: '12',
                  icon: Icons.store,
                  color: Colors.blue,
                ),
                _buildStatCard(
                  title: 'Agents',
                  value: '45',
                  icon: Icons.people,
                  color: Colors.green,
                ),
                _buildStatCard(
                  title: 'Contrats',
                  value: '1,234',
                  icon: Icons.description,
                  color: Colors.orange,
                ),
                _buildStatCard(
                  title: 'Sinistres',
                  value: '89',
                  icon: Icons.report_problem,
                  color: Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Construire une carte de statistique
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 48, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 🏪 Vue gestion agences
  Widget _buildAgencesView() {
    return const Center(
      child: Text('Gestion des Agences - En développement'),
    );
  }

  /// 👥 Vue gestion agents
  Widget _buildAgentsView() {
    return const Center(
      child: Text('Gestion des Agents - En développement'),
    );
  }

  /// 📄 Vue contrats
  Widget _buildContratsView() {
    return const Center(
      child: Text('Gestion des Contrats - En développement'),
    );
  }

  /// 🚨 Vue sinistres
  Widget _buildSinistresView() {
    return const Center(
      child: Text('Gestion des Sinistres - En développement'),
    );
  }

  /// 📈 Vue statistiques
  Widget _buildStatistiquesView() {
    return const Center(
      child: Text('Statistiques - En développement'),
    );
  }

  /// ⚙️ Vue paramètres
  Widget _buildParametresView() {
    return const Center(
      child: Text('Paramètres - En développement'),
    );
  }

  /// 👤 Afficher le profil
  void _showProfile() {
    // TODO: Implémenter l'affichage du profil
  }

  /// ⚙️ Afficher les paramètres
  void _showSettings() {
    // TODO: Implémenter les paramètres
  }

  /// 🚪 Déconnexion
  void _logout() async {
    await _auth.signOut();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }
}
