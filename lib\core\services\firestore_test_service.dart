import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// 🧪 Service de test Firestore
/// 
/// Tests simples pour vérifier que Firestore fonctionne
class FirestoreTestService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🧪 Test complet de Firestore
  static Future<Map<String, dynamic>> runCompleteTest() async {
    debugPrint('[FIRESTORE_TEST] 🧪 Début test complet Firestore...');
    
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'success': false,
    };

    try {
      // Test 1: Lecture simple
      await _testSimpleRead(results);
      
      // Test 2: Écriture simple
      await _testSimpleWrite(results);
      
      // Test 3: Lecture avec authentification
      await _testAuthenticatedRead(results);
      
      // Test 4: Test des règles de sécurité
      await _testSecurityRules(results);

      // Analyser les résultats
      final testResults = results['tests'] as Map<String, dynamic>;
      final successCount = testResults.values.where((test) => test['success'] == true).length;
      results['success'] = successCount >= 2; // Au moins 2 tests doivent réussir
      
      debugPrint('[FIRESTORE_TEST] 📊 Tests terminés: $successCount/${testResults.length} réussis');
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Erreur générale: $e');
      results['error'] = e.toString();
    }

    return results;
  }

  /// 📖 Test lecture simple
  static Future<void> _testSimpleRead(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_TEST] 📖 Test lecture simple...');
      
      final startTime = DateTime.now();
      
      // Essayer de lire une collection publique
      final snapshot = await _firestore
          .collection('health_check')
          .limit(1)
          .get(const GetOptions(source: Source.server))
          .timeout(const Duration(seconds: 10));
      
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      results['tests']['simple_read'] = {
        'success': true,
        'duration_ms': duration,
        'documents_count': snapshot.docs.length,
        'message': 'Lecture simple réussie',
      };
      
      debugPrint('[FIRESTORE_TEST] ✅ Lecture simple OK (${duration}ms)');
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Lecture simple échouée: $e');
      
      results['tests']['simple_read'] = {
        'success': false,
        'error': e.toString(),
        'message': 'Échec lecture simple',
      };
    }
  }

  /// ✏️ Test écriture simple
  static Future<void> _testSimpleWrite(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_TEST] ✏️ Test écriture simple...');
      
      final startTime = DateTime.now();
      final testId = 'test_${DateTime.now().millisecondsSinceEpoch}';
      
      // Essayer d'écrire dans une collection de test
      await _firestore
          .collection('health_check')
          .doc(testId)
          .set({
        'test': true,
        'timestamp': FieldValue.serverTimestamp(),
        'created_by': 'firestore_test_service',
      }).timeout(const Duration(seconds: 10));
      
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      results['tests']['simple_write'] = {
        'success': true,
        'duration_ms': duration,
        'document_id': testId,
        'message': 'Écriture simple réussie',
      };
      
      debugPrint('[FIRESTORE_TEST] ✅ Écriture simple OK (${duration}ms)');
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Écriture simple échouée: $e');
      
      results['tests']['simple_write'] = {
        'success': false,
        'error': e.toString(),
        'message': 'Échec écriture simple',
      };
    }
  }

  /// 🔐 Test lecture avec authentification
  static Future<void> _testAuthenticatedRead(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_TEST] 🔐 Test lecture authentifiée...');
      
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        results['tests']['authenticated_read'] = {
          'success': false,
          'message': 'Aucun utilisateur connecté',
        };
        return;
      }
      
      final startTime = DateTime.now();
      
      // Essayer de lire le document utilisateur
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get(const GetOptions(source: Source.server))
          .timeout(const Duration(seconds: 10));
      
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      
      results['tests']['authenticated_read'] = {
        'success': true,
        'duration_ms': duration,
        'user_exists': userDoc.exists,
        'user_data': userDoc.exists ? userDoc.data() : null,
        'message': 'Lecture authentifiée réussie',
      };
      
      debugPrint('[FIRESTORE_TEST] ✅ Lecture authentifiée OK (${duration}ms)');
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Lecture authentifiée échouée: $e');
      
      results['tests']['authenticated_read'] = {
        'success': false,
        'error': e.toString(),
        'message': 'Échec lecture authentifiée',
      };
    }
  }

  /// 🛡️ Test règles de sécurité
  static Future<void> _testSecurityRules(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_TEST] 🛡️ Test règles de sécurité...');
      
      // Test lecture collection users (devrait être autorisée pour utilisateur connecté)
      try {
        await _firestore
            .collection('users')
            .limit(1)
            .get(const GetOptions(source: Source.server))
            .timeout(const Duration(seconds: 10));
        
        results['tests']['security_rules'] = {
          'success': true,
          'message': 'Règles de sécurité OK',
        };
        
        debugPrint('[FIRESTORE_TEST] ✅ Règles de sécurité OK');
        
      } catch (e) {
        if (e.toString().contains('permission-denied')) {
          results['tests']['security_rules'] = {
            'success': false,
            'error': 'Permission refusée',
            'message': 'Règles de sécurité trop restrictives',
          };
        } else {
          results['tests']['security_rules'] = {
            'success': false,
            'error': e.toString(),
            'message': 'Erreur test règles de sécurité',
          };
        }
        
        debugPrint('[FIRESTORE_TEST] ❌ Test règles échoué: $e');
      }
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Erreur test règles: $e');
      
      results['tests']['security_rules'] = {
        'success': false,
        'error': e.toString(),
        'message': 'Erreur générale test règles',
      };
    }
  }

  /// 🎯 Test rapide de disponibilité
  static Future<bool> isFirestoreWorking() async {
    try {
      debugPrint('[FIRESTORE_TEST] 🎯 Test rapide disponibilité...');
      
      await _firestore
          .collection('health_check')
          .doc('ping')
          .get(const GetOptions(source: Source.server))
          .timeout(const Duration(seconds: 5));
      
      debugPrint('[FIRESTORE_TEST] ✅ Firestore disponible');
      return true;
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Firestore indisponible: $e');
      return false;
    }
  }

  /// 🔧 Forcer la reconnexion Firestore
  static Future<void> forceReconnect() async {
    try {
      debugPrint('[FIRESTORE_TEST] 🔧 Force reconnexion Firestore...');
      
      // Désactiver puis réactiver le réseau Firestore
      await _firestore.disableNetwork();
      await Future.delayed(const Duration(seconds: 1));
      await _firestore.enableNetwork();
      
      debugPrint('[FIRESTORE_TEST] ✅ Reconnexion forcée terminée');
      
    } catch (e) {
      debugPrint('[FIRESTORE_TEST] ❌ Erreur reconnexion: $e');
    }
  }

  /// 📊 Afficher un rapport de test formaté
  static String formatTestReport(Map<String, dynamic> testResults) {
    final buffer = StringBuffer();
    
    buffer.writeln('🧪 RAPPORT DE TEST FIRESTORE');
    buffer.writeln('=' * 40);
    buffer.writeln('📅 ${testResults['timestamp']}');
    buffer.writeln('🎯 Succès global: ${testResults['success']}');
    buffer.writeln();
    
    buffer.writeln('📊 DÉTAILS DES TESTS:');
    final tests = testResults['tests'] as Map<String, dynamic>;
    tests.forEach((testName, result) {
      final success = result['success'] == true;
      final icon = success ? '✅' : '❌';
      final message = result['message'] ?? 'Pas de message';
      
      buffer.writeln('  $icon $testName: $message');
      
      if (result['duration_ms'] != null) {
        buffer.writeln('    ⏱️ Durée: ${result['duration_ms']}ms');
      }
      
      if (!success && result['error'] != null) {
        buffer.writeln('    🔍 Erreur: ${result['error']}');
      }
    });
    
    return buffer.toString();
  }
}
