import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/offline_admin_service.dart';

/// 🚀 Service de création d'utilisateur SIMPLE et FIABLE
/// 
/// Essaie Firebase, sinon mode hors ligne IMMÉDIATEMENT
class SimpleUserCreationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚀 Créer un Admin Compagnie (méthode simple et fiable)
  static Future<Map<String, dynamic>> createAdminCompagnie({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
    String? address,
  }) async {
    debugPrint('[SIMPLE_USER_CREATION] 🚀 Création Admin Compagnie...');
    debugPrint('[SIMPLE_USER_CREATION] 📧 Email: $email');
    debugPrint('[SIMPLE_USER_CREATION] 🏢 Compagnie: $compagnieNom');

    // ÉTAPE 1: Essayer Firebase avec timeout court
    try {
      debugPrint('[SIMPLE_USER_CREATION] 🔥 Tentative Firebase...');
      
      final result = await _createViaFirebase(
        email: email,
        nom: nom,
        prenom: prenom,
        compagnieId: compagnieId,
        compagnieNom: compagnieNom,
        phone: phone,
        address: address,
      ).timeout(const Duration(seconds: 5)); // Timeout court !

      debugPrint('[SIMPLE_USER_CREATION] ✅ Création Firebase réussie !');
      return result;

    } catch (e) {
      debugPrint('[SIMPLE_USER_CREATION] ⚠️ Firebase échoué: $e');
      debugPrint('[SIMPLE_USER_CREATION] 🚀 Passage mode hors ligne...');
    }

    // ÉTAPE 2: Mode hors ligne immédiat
    try {
      final result = await OfflineAdminService.createAdminCompagnieOffline(
        email: email,
        nom: nom,
        prenom: prenom,
        compagnieId: compagnieId,
        compagnieNom: compagnieNom,
        phone: phone,
        address: address,
      );

      debugPrint('[SIMPLE_USER_CREATION] ✅ Création hors ligne réussie !');
      return result;

    } catch (e) {
      debugPrint('[SIMPLE_USER_CREATION] ❌ Échec total: $e');
      return {
        'success': false,
        'error': 'Échec création utilisateur: $e',
        'method': 'failed',
      };
    }
  }

  /// 🔥 Créer via Firebase (avec timeout)
  static Future<Map<String, dynamic>> _createViaFirebase({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
    String? address,
  }) async {
    // Générer mot de passe
    final tempPassword = _generateSecurePassword();

    // Créer compte Firebase Auth
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: tempPassword,
    );

    final userId = userCredential.user!.uid;

    // Créer document Firestore
    await _firestore
        .collection('users')
        .doc(userId)
        .set({
      'uid': userId,
      'email': email,
      'nom': nom,
      'prenom': prenom,
      'role': 'admin_compagnie',
      'compagnieId': compagnieId,
      'compagnieNom': compagnieNom,
      'phone': phone,
      'address': address,
      'isFirstLogin': true,
      'isActive': true,
      'status': 'actif',
      'created_at': FieldValue.serverTimestamp(),
      'created_by': 'super_admin',
      'source': 'direct_creation',
    });

    return {
      'success': true,
      'userId': userId,
      'email': email,
      'temporaryPassword': tempPassword,
      'method': 'firebase',
      'message': 'Admin Compagnie créé avec succès via Firebase',
    };
  }

  /// 🔐 Générer mot de passe sécurisé
  static String _generateSecurePassword() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp % 10000;
    return 'Admin$random!';
  }

  /// 🧪 Test rapide Firebase
  static Future<bool> isFirebaseWorking() async {
    try {
      await _firestore
          .collection('health_check')
          .doc('test')
          .get()
          .timeout(const Duration(seconds: 3));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 📊 Statistiques de création
  static Future<Map<String, dynamic>> getStats() async {
    return {
      'firebase_available': await isFirebaseWorking(),
      'service_status': 'operational',
      'last_check': DateTime.now().toIso8601String(),
    };
  }
}
