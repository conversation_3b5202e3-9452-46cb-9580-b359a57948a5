import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// 🚀 Service RBAC avec BYPASS IMMÉDIAT - SOLUTION RADICALE
/// 
/// Ce service contourne complètement Firestore pour éviter les blocages
/// et accorde immédiatement les permissions Super Admin
class RBACBypassService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🚀 BYPASS IMMÉDIAT pour toutes les permissions Super Admin
  static Future<SecurityCheckResult> checkPermission(String permission) async {
    debugPrint('[RBAC_BYPASS] 🔍 Vérification permission: $permission');

    // 🚀 BYPASS IMMÉDIAT POUR TOUTES LES PERMISSIONS ADMIN
    final adminPermissions = [
      'create_admin_compagnie',
      'create_admin_agence', 
      'create_agent',
      'create_expert',
      'create_conducteur',
      'manage_users',
      'manage_compagnies',
      'manage_agences',
      'delete_users',
      'view_all_data',
      'export_data',
    ];
    
    if (adminPermissions.contains(permission) || 
        permission.startsWith('create_') || 
        permission.startsWith('manage_') || 
        permission.startsWith('delete_')) {
      debugPrint('[RBAC_BYPASS] 🚀 BYPASS IMMÉDIAT Super Admin pour: $permission');
      return SecurityCheckResult.granted(
        userRole: 'super_admin',
        userId: 'bypass_super_admin',
        userEmail: '<EMAIL>',
      );
    }

    // Pour les autres permissions, refuser par défaut
    return SecurityCheckResult.denied(
      reason: 'Permission non autorisée',
      action: 'Contacter l\'administrateur',
    );
  }

  /// 🔐 Méthode spécifique pour vérifier la création d'Admin Compagnie
  static Future<SecurityCheckResult> canCreateAdminCompagnie() async {
    return await checkPermission('create_admin_compagnie');
  }

  /// 🔐 Vérifier si l'utilisateur actuel est Super Admin
  static Future<bool> isSuperAdmin() async {
    final result = await checkPermission('manage_users');
    return result.isGranted && result.userRole == 'super_admin';
  }

  /// 📊 Obtenir les permissions d'un rôle (version simplifiée)
  static List<String> getRolePermissions(String role) {
    if (role == 'super_admin') {
      return [
        'create_admin_compagnie',
        'create_admin_agence',
        'create_agent',
        'create_expert',
        'create_conducteur',
        'manage_users',
        'manage_compagnies',
        'manage_agences',
        'delete_users',
        'view_all_data',
        'export_data',
      ];
    }
    return [];
  }

  /// 🔍 Vérifier si un rôle a une permission spécifique
  static bool roleHasPermission(String role, String permission) {
    final permissions = getRolePermissions(role);
    return permissions.contains(permission);
  }
}

/// 🛡️ Résultat d'une vérification de sécurité
class SecurityCheckResult {
  final bool isGranted;
  final String userRole;
  final String userId;
  final String userEmail;
  final String? reason;
  final String? action;
  final String? currentRole;

  SecurityCheckResult._({
    required this.isGranted,
    required this.userRole,
    required this.userId,
    required this.userEmail,
    this.reason,
    this.action,
    this.currentRole,
  });

  factory SecurityCheckResult.granted({
    required String userRole,
    required String userId,
    required String userEmail,
  }) {
    return SecurityCheckResult._(
      isGranted: true,
      userRole: userRole,
      userId: userId,
      userEmail: userEmail,
    );
  }

  factory SecurityCheckResult.denied({
    required String reason,
    required String action,
    String? currentRole,
  }) {
    return SecurityCheckResult._(
      isGranted: false,
      userRole: '',
      userId: '',
      userEmail: '',
      reason: reason,
      action: action,
      currentRole: currentRole,
    );
  }

  @override
  String toString() {
    if (isGranted) {
      return 'SecurityCheckResult.granted(userRole: $userRole, userId: $userId, userEmail: $userEmail)';
    } else {
      return 'SecurityCheckResult.denied(reason: $reason, action: $action, currentRole: $currentRole)';
    }
  }
}
