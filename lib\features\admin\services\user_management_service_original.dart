import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../../../core/services/password_generation_service.dart';
import '../../../core/services/email_service.dart';
import 'audit_logger_service.dart';

/// 👥 Service complet de gestion des utilisateurs pour Super Admin
class UserManagementService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 📊 Récupérer tous les utilisateurs avec pagination
  static Future<Map<String, dynamic>> getAllUsers({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    String? roleFilter,
    String? statusFilter,
    String? compagnieFilter,
    String? agenceFilter,
  }) async {
    try {
      debugPrint('[USER_MANAGEMENT] 📊 Récupération utilisateurs...');

      Query query = _firestore.collection('users');

      // Filtres
      if (roleFilter != null && roleFilter != 'tous') {
        query = query.where('role', isEqualTo: roleFilter);
      }
      if (statusFilter != null && statusFilter != 'tous') {
        query = query.where('status', isEqualTo: statusFilter);
      }
      if (compagnieFilter != null && compagnieFilter != 'toutes') {
        query = query.where('compagnieId', isEqualTo: compagnieFilter);
      }
      if (agenceFilter != null && agenceFilter != 'toutes') {
        query = query.where('agenceId', isEqualTo: agenceFilter);
      }

      // Pagination
      query = query.orderBy('created_at', descending: true).limit(limit);
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      final snapshot = await query.get();
      
      final users = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'uid': doc.id,
          ...data,
        };
      }).toList();

      debugPrint('[USER_MANAGEMENT] ✅ ${users.length} utilisateurs récupérés');

      return {
        'users': users,
        'lastDocument': snapshot.docs.isNotEmpty ? snapshot.docs.last : null,
        'hasMore': snapshot.docs.length == limit,
      };

    } catch (e) {
      debugPrint('[USER_MANAGEMENT] ❌ Erreur récupération: $e');
      return {
        'users': <Map<String, dynamic>>[],
        'lastDocument': null,
        'hasMore': false,
        'error': e.toString(),
      };
    }
  }

  /// 👤 Créer un nouvel utilisateur
  static Future<Map<String, dynamic>> createUser({
    required String email,
    required String nom,
    required String prenom,
    required String role,
    required String compagnieId,
    String? agenceId,
    String? phone,
    String? address,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      debugPrint('[USER_MANAGEMENT] 👤 Création utilisateur: $email');

      // Générer un mot de passe temporaire
      final tempPassword = PasswordGenerationService.generateSecurePassword();

      // Créer le compte Firebase Auth
      UserCredential userCredential;
      try {
        userCredential = await _auth.createUserWithEmailAndPassword(
          email: email,
          password: tempPassword,
        );
      } catch (authError) {
        debugPrint('[USER_MANAGEMENT] ❌ Erreur Firebase Auth: $authError');
        return {
          'success': false,
          'error': 'Erreur création compte: ${authError.toString()}',
        };
      }

      final user = userCredential.user!;

      // Préparer les données utilisateur
      final userData = {
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': role,
        'status': 'actif',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'phone': phone,
        'address': address,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': _auth.currentUser?.uid,
        'created_by_email': _auth.currentUser?.email,
        'password_reset_required': true,
        'last_login': null,
        'isFakeData': false,
        'isLegitimate': true,
        'accessLevel': 'production',
        ...?additionalData,
      };

      // Sauvegarder dans Firestore
      await _firestore.collection('users').doc(user.uid).set(userData);

      // Envoyer l'email avec les identifiants
      try {
        await EmailService.sendAccountCreationEmail(
          email: email,
          password: tempPassword,
          userName: '$prenom $nom',
          role: role,
        );
        debugPrint('[USER_MANAGEMENT] ✅ Email envoyé');
      } catch (emailError) {
        debugPrint('[USER_MANAGEMENT] ⚠️ Erreur envoi email: $emailError');
        // Le compte est créé mais l'email a échoué
      }

      // Logger l'action
      await AuditLoggerService.log(
        action: 'user_created',
        performedBy: _auth.currentUser?.uid ?? 'unknown',
        targetUserEmail: email,
        details: {
          'role': role,
          'compagnieId': compagnieId,
          'agenceId': agenceId,
        },
        level: AuditLoggerService.LEVEL_MEDIUM,
      );

      debugPrint('[USER_MANAGEMENT] ✅ Utilisateur créé: ${user.uid}');

      return {
        'success': true,
        'uid': user.uid,
        'email': email,
        'tempPassword': tempPassword,
        'userData': userData,
      };

    } catch (e) {
      debugPrint('[USER_MANAGEMENT] ❌ Erreur création: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// ✏️ Mettre à jour un utilisateur
  static Future<Map<String, dynamic>> updateUser({
    required String uid,
    String? nom,
    String? prenom,
    String? phone,
    String? address,
    String? role,
    String? status,
    String? compagnieId,
    String? agenceId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      debugPrint('[USER_MANAGEMENT] ✏️ Mise à jour utilisateur: $uid');

      // Récupérer les données actuelles
      final currentDoc = await _firestore.collection('users').doc(uid).get();
      if (!currentDoc.exists) {
        return {
          'success': false,
          'error': 'Utilisateur non trouvé',
        };
      }

      final currentData = currentDoc.data()!;
      
      // Préparer les nouvelles données
      final updateData = <String, dynamic>{
        'updated_at': FieldValue.serverTimestamp(),
        'updated_by': _auth.currentUser?.uid,
        'updated_by_email': _auth.currentUser?.email,
      };

      if (nom != null) updateData['nom'] = nom;
      if (prenom != null) updateData['prenom'] = prenom;
      if (phone != null) updateData['phone'] = phone;
      if (address != null) updateData['address'] = address;
      if (role != null) updateData['role'] = role;
      if (status != null) updateData['status'] = status;
      if (compagnieId != null) updateData['compagnieId'] = compagnieId;
      if (agenceId != null) updateData['agenceId'] = agenceId;
      if (additionalData != null) updateData.addAll(additionalData);

      // Mettre à jour dans Firestore
      await _firestore.collection('users').doc(uid).update(updateData);

      // Logger l'action
      await AuditLoggerService.log(
        action: 'user_updated',
        performedBy: _auth.currentUser?.uid ?? 'unknown',
        targetUserEmail: currentData['email'],
        details: {
          'changes': updateData,
          'previousRole': currentData['role'],
          'newRole': role,
        },
        level: AuditLoggerService.LEVEL_MEDIUM,
      );

      debugPrint('[USER_MANAGEMENT] ✅ Utilisateur mis à jour: $uid');

      return {
        'success': true,
        'uid': uid,
        'updateData': updateData,
      };

    } catch (e) {
      debugPrint('[USER_MANAGEMENT] ❌ Erreur mise à jour: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🗑️ Supprimer un utilisateur
  static Future<Map<String, dynamic>> deleteUser(String uid) async {
    try {
      debugPrint('[USER_MANAGEMENT] 🗑️ Suppression utilisateur: $uid');

      // Récupérer les données avant suppression
      final userDoc = await _firestore.collection('users').doc(uid).get();
      if (!userDoc.exists) {
        return {
          'success': false,
          'error': 'Utilisateur non trouvé',
        };
      }

      final userData = userDoc.data()!;
      final userEmail = userData['email'];

      // Supprimer de Firestore
      await _firestore.collection('users').doc(uid).delete();

      // Logger l'action
      await AuditLoggerService.log(
        action: 'user_deleted',
        performedBy: _auth.currentUser?.uid ?? 'unknown',
        targetUserEmail: userEmail,
        details: {
          'deletedUserData': userData,
        },
        level: AuditLoggerService.LEVEL_HIGH,
      );

      debugPrint('[USER_MANAGEMENT] ✅ Utilisateur supprimé: $uid');

      return {
        'success': true,
        'uid': uid,
        'deletedUserData': userData,
      };

    } catch (e) {
      debugPrint('[USER_MANAGEMENT] ❌ Erreur suppression: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 🔄 Réinitialiser le mot de passe
  static Future<Map<String, dynamic>> resetUserPassword(String uid) async {
    try {
      debugPrint('[USER_MANAGEMENT] 🔄 Réinitialisation mot de passe: $uid');

      // Récupérer l'email de l'utilisateur
      final userDoc = await _firestore.collection('users').doc(uid).get();
      if (!userDoc.exists) {
        return {
          'success': false,
          'error': 'Utilisateur non trouvé',
        };
      }

      final userData = userDoc.data()!;
      final email = userData['email'];

      // Générer un nouveau mot de passe
      final newPassword = PasswordGenerationService.generateSecurePassword();

      // Mettre à jour le mot de passe dans Firebase Auth
      // Note: Ceci nécessite des privilèges admin
      
      // Marquer que le mot de passe doit être changé
      await _firestore.collection('users').doc(uid).update({
        'password_reset_required': true,
        'password_reset_at': FieldValue.serverTimestamp(),
        'password_reset_by': _auth.currentUser?.uid,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Envoyer l'email avec le nouveau mot de passe
      try {
        await EmailService.sendPasswordResetEmail(
          email: email,
          newPassword: newPassword,
          userName: '${userData['prenom']} ${userData['nom']}',
        );
      } catch (emailError) {
        debugPrint('[USER_MANAGEMENT] ⚠️ Erreur envoi email: $emailError');
      }

      // Logger l'action
      await AuditLoggerService.log(
        action: 'password_reset',
        performedBy: _auth.currentUser?.uid ?? 'unknown',
        targetUserEmail: email,
        details: {
          'resetMethod': 'admin_reset',
        },
        level: AuditLoggerService.LEVEL_MEDIUM,
      );

      debugPrint('[USER_MANAGEMENT] ✅ Mot de passe réinitialisé: $uid');

      return {
        'success': true,
        'uid': uid,
        'newPassword': newPassword,
        'email': email,
      };

    } catch (e) {
      debugPrint('[USER_MANAGEMENT] ❌ Erreur réinitialisation: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
