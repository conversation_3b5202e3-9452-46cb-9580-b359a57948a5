import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// 🚀 Service IMMÉDIAT pour créer le Super Admin
/// 
/// GARANTIT que le Super Admin existe et fonctionne
class ImmediateSuperAdminService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // 🔐 Informations Super Admin
  static const String SUPER_ADMIN_EMAIL = '<EMAIL>';
  static const String SUPER_ADMIN_PASSWORD = 'Acheya123';

  /// 🚀 CRÉER LE SUPER ADMIN IMMÉDIATEMENT
  static Future<bool> createSuperAdminNow() async {
    try {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🚀 CRÉATION IMMÉDIATE Super Admin...');

      // ÉTAPE 1: Vérifier si le compte Firebase Auth existe
      UserCredential? userCredential;
      
      try {
        // Essayer de se connecter
        userCredential = await _auth.signInWithEmailAndPassword(
          email: SUPER_ADMIN_EMAIL,
          password: SUPER_ADMIN_PASSWORD,
        );
        debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Compte Firebase Auth existe déjà');
        
      } catch (e) {
        debugPrint('[IMMEDIATE_SUPER_ADMIN] 🔧 Création compte Firebase Auth...');
        
        try {
          // Créer le compte
          userCredential = await _auth.createUserWithEmailAndPassword(
            email: SUPER_ADMIN_EMAIL,
            password: SUPER_ADMIN_PASSWORD,
          );
          debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Compte Firebase Auth créé');
          
        } catch (createError) {
          if (createError.toString().contains('email-already-in-use')) {
            // Le compte existe, essayer de se connecter à nouveau
            userCredential = await _auth.signInWithEmailAndPassword(
              email: SUPER_ADMIN_EMAIL,
              password: SUPER_ADMIN_PASSWORD,
            );
            debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Compte existant récupéré');
          } else {
            throw createError;
          }
        }
      }

      final userId = userCredential!.user!.uid;
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🆔 UID: $userId');

      // ÉTAPE 2: Créer/Mettre à jour le document Firestore
      await _createFirestoreDocument(userId);

      // ÉTAPE 3: Se déconnecter
      await _auth.signOut();
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🚪 Déconnexion effectuée');

      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🎉 SUPER ADMIN PRÊT !');
      return true;

    } catch (e) {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] ❌ ERREUR: $e');
      return false;
    }
  }

  /// 📄 Créer le document Firestore
  static Future<void> _createFirestoreDocument(String userId) async {
    try {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 📄 Création document Firestore...');

      final superAdminData = {
        'uid': userId,
        'email': SUPER_ADMIN_EMAIL,
        'role': 'super_admin',
        'nom': 'Super',
        'prenom': 'Admin',
        'displayName': 'Super Admin',
        'phone': '+216 70 000 000',
        'status': 'actif',
        'isActive': true,
        'isLegitimate': true,
        'accessLevel': 'production',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'immediate_service',
        'origin': 'system_creation',
        'permissions': [
          'create_admin_compagnie',
          'create_admin_agence',
          'create_agent',
          'create_expert',
          'manage_all_users',
          'manage_all_companies',
          'view_all_data',
          'system_administration',
        ],
      };

      // Essayer avec timeout court
      await _firestore
          .collection('users')
          .doc(userId)
          .set(superAdminData, SetOptions(merge: true))
          .timeout(const Duration(seconds: 10));

      debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Document Firestore créé');

    } catch (e) {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] ⚠️ Erreur Firestore (pas critique): $e');
      // Ne pas faire échouer la création pour un problème Firestore
    }
  }

  /// 🧪 Tester la connexion Super Admin
  static Future<bool> testSuperAdminLogin() async {
    try {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🧪 Test connexion Super Admin...');

      final userCredential = await _auth.signInWithEmailAndPassword(
        email: SUPER_ADMIN_EMAIL,
        password: SUPER_ADMIN_PASSWORD,
      );

      debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Test connexion réussi');
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🆔 UID: ${userCredential.user!.uid}');
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 📧 Email: ${userCredential.user!.email}');

      await _auth.signOut();
      return true;

    } catch (e) {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] ❌ Test connexion échoué: $e');
      return false;
    }
  }

  /// 🔍 Vérifier si le Super Admin existe
  static Future<bool> checkSuperAdminExists() async {
    try {
      // Test simple de connexion
      return await testSuperAdminLogin();
    } catch (e) {
      return false;
    }
  }

  /// 📊 Obtenir les informations du Super Admin
  static Map<String, dynamic> getSuperAdminInfo() {
    return {
      'email': SUPER_ADMIN_EMAIL,
      'password': SUPER_ADMIN_PASSWORD,
      'role': 'super_admin',
      'nom': 'Super',
      'prenom': 'Admin',
      'phone': '+216 70 000 000',
      'status': 'actif',
    };
  }

  /// 🚀 INITIALISATION COMPLÈTE (à appeler au démarrage)
  static Future<void> initializeOnStartup() async {
    try {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🚀 Initialisation au démarrage...');

      // Vérifier si le Super Admin existe
      final exists = await checkSuperAdminExists();
      
      if (!exists) {
        debugPrint('[IMMEDIATE_SUPER_ADMIN] 🔧 Super Admin n\'existe pas, création...');
        final created = await createSuperAdminNow();
        
        if (created) {
          debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Super Admin créé avec succès');
        } else {
          debugPrint('[IMMEDIATE_SUPER_ADMIN] ❌ Échec création Super Admin');
        }
      } else {
        debugPrint('[IMMEDIATE_SUPER_ADMIN] ✅ Super Admin existe déjà');
      }

    } catch (e) {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] ❌ Erreur initialisation: $e');
    }
  }

  /// 🔧 Forcer la recréation du Super Admin
  static Future<bool> forceRecreateSuperAdmin() async {
    try {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] 🔧 FORCE RECRÉATION Super Admin...');

      // Supprimer le compte existant si possible
      try {
        final userCredential = await _auth.signInWithEmailAndPassword(
          email: SUPER_ADMIN_EMAIL,
          password: SUPER_ADMIN_PASSWORD,
        );
        
        // Supprimer le document Firestore
        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .delete()
            .timeout(const Duration(seconds: 5));
        
        // Supprimer le compte Firebase Auth
        await userCredential.user!.delete();
        debugPrint('[IMMEDIATE_SUPER_ADMIN] 🗑️ Ancien compte supprimé');
        
      } catch (e) {
        debugPrint('[IMMEDIATE_SUPER_ADMIN] ⚠️ Suppression échouée (normal): $e');
      }

      // Recréer le compte
      return await createSuperAdminNow();

    } catch (e) {
      debugPrint('[IMMEDIATE_SUPER_ADMIN] ❌ Erreur force recréation: $e');
      return false;
    }
  }
}
