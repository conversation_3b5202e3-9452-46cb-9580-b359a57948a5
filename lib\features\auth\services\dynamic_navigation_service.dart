import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../../admin_compagnie/services/company_redirect_service.dart';

/// 🧭 Service de navigation dynamique basé sur le rôle et les permissions
class DynamicNavigationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔍 Analyser l'utilisateur connecté et déterminer sa redirection
  static Future<Map<String, dynamic>> analyzeUserAndGetRedirection() async {
    try {
      debugPrint('[DYNAMIC_NAV] 🔍 Analyse de l\'utilisateur connecté...');

      // 1. Récupérer l'utilisateur Firebase Auth
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('[DYNAMIC_NAV] ❌ Aucun utilisateur connecté');
        return {
          'success': false,
          'error': 'Aucun utilisateur connecté',
          'redirectTo': '/login',
        };
      }

      debugPrint('[DYNAMIC_NAV] ✅ Utilisateur Firebase Auth: ${currentUser.email}');

      // 2. Récupérer les données Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        debugPrint('[DYNAMIC_NAV] ❌ Document Firestore non trouvé');
        return {
          'success': false,
          'error': 'Compte non configuré dans Firestore',
          'redirectTo': '/login',
        };
      }

      final userData = userDoc.data()!;
      final role = userData['role'] as String?;
      final status = userData['status'] as String?;
      final compagnieId = userData['compagnieId'] as String?;

      debugPrint('[DYNAMIC_NAV] 📋 Données utilisateur:');
      debugPrint('[DYNAMIC_NAV]   - Email: ${currentUser.email}');
      debugPrint('[DYNAMIC_NAV]   - Rôle: $role');
      debugPrint('[DYNAMIC_NAV]   - Statut: $status');
      debugPrint('[DYNAMIC_NAV]   - CompagnieId: $compagnieId');

      // 3. Vérifier le statut du compte
      if (status != 'actif') {
        debugPrint('[DYNAMIC_NAV] ❌ Compte inactif: $status');
        return {
          'success': false,
          'error': 'Compte inactif ou suspendu',
          'redirectTo': '/login',
        };
      }

      // 4. Déterminer la redirection selon le rôle
      return await _getRedirectionByRole(role, userData, currentUser);

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur analyse: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
        'redirectTo': '/login',
      };
    }
  }

  /// 🎯 Déterminer la redirection selon le rôle
  static Future<Map<String, dynamic>> _getRedirectionByRole(
    String? role,
    Map<String, dynamic> userData,
    User currentUser,
  ) async {
    switch (role) {
      case 'super_admin':
        debugPrint('[DYNAMIC_NAV] 👑 Super Admin détecté');
        return {
          'success': true,
          'role': 'super_admin',
          'redirectTo': '/super-admin-dashboard',
          'userData': userData,
        };

      case 'admin_compagnie':
        debugPrint('[DYNAMIC_NAV] 🏢 Admin Compagnie détecté');
        return await _handleAdminCompagnieRedirection(userData, currentUser);

      case 'admin_agence':
        debugPrint('[DYNAMIC_NAV] 🏛️ Admin Agence détecté');
        return await _handleAdminAgenceRedirection(userData, currentUser);

      case 'agent':
        debugPrint('[DYNAMIC_NAV] 👨‍💼 Agent détecté');
        return {
          'success': true,
          'role': 'agent',
          'redirectTo': '/agent-dashboard',
          'userData': userData,
        };

      case 'conducteur':
        debugPrint('[DYNAMIC_NAV] 🚗 Conducteur détecté');
        return {
          'success': true,
          'role': 'conducteur',
          'redirectTo': '/conducteur-dashboard',
          'userData': userData,
        };

      case 'expert':
        debugPrint('[DYNAMIC_NAV] 🔍 Expert détecté');
        return {
          'success': true,
          'role': 'expert',
          'redirectTo': '/expert-dashboard',
          'userData': userData,
        };

      default:
        debugPrint('[DYNAMIC_NAV] ❌ Rôle non reconnu: $role');
        return {
          'success': false,
          'error': 'Rôle non reconnu: $role',
          'redirectTo': '/login',
        };
    }
  }

  /// 🏢 Gestion spécifique Admin Compagnie avec validation de légitimité
  static Future<Map<String, dynamic>> _handleAdminCompagnieRedirection(
    Map<String, dynamic> userData,
    User currentUser,
  ) async {
    try {
      final compagnieId = userData['compagnieId'] as String?;

      if (compagnieId == null || compagnieId.isEmpty) {
        debugPrint('[DYNAMIC_NAV] ❌ Admin Compagnie sans compagnieId');
        return {
          'success': false,
          'error': 'Compte Admin Compagnie mal configuré (compagnieId manquant)',
          'redirectTo': '/login',
        };
      }

      // 🔐 VALIDATION DE LÉGITIMITÉ
      final legitimacyCheck = await _validateAdminCompagnieLegitimacy(userData);
      if (!legitimacyCheck['isLegitimate']) {
        debugPrint('[DYNAMIC_NAV] ❌ Compte Admin Compagnie non légitime: ${legitimacyCheck['reason']}');
        return {
          'success': false,
          'error': 'Accès refusé: ${legitimacyCheck['reason']}',
          'redirectTo': '/login',
        };
      }

      // Vérifier que la compagnie existe
      final compagnieDoc = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (!compagnieDoc.exists) {
        debugPrint('[DYNAMIC_NAV] ❌ Compagnie non trouvée: $compagnieId');
        return {
          'success': false,
          'error': 'Compagnie associée non trouvée',
          'redirectTo': '/login',
        };
      }

      final compagnieData = compagnieDoc.data()!;
      final compagnieNom = compagnieData['nom'] as String?;

      debugPrint('[DYNAMIC_NAV] ✅ Admin Compagnie légitime et valide:');
      debugPrint('[DYNAMIC_NAV]   - CompagnieId: $compagnieId');
      debugPrint('[DYNAMIC_NAV]   - CompagnieNom: $compagnieNom');
      debugPrint('[DYNAMIC_NAV]   - Créé par: ${userData['created_by']}');

      // 🔄 Utiliser le service de redirection dynamique
      final redirectRoute = await CompanyRedirectService.getCompanyDashboardRoute(currentUser.uid);

      return {
        'success': true,
        'role': 'admin_compagnie',
        'redirectTo': redirectRoute ?? '/compagnie-dashboard/$compagnieId',
        'userData': userData,
        'compagnieData': compagnieData,
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
      };

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur Admin Compagnie: $e');
      return {
        'success': false,
        'error': 'Erreur validation Admin Compagnie: $e',
        'redirectTo': '/login',
      };
    }
  }

  /// 🔐 Valider la légitimité d'un compte Admin Compagnie
  static Future<Map<String, dynamic>> _validateAdminCompagnieLegitimacy(
    Map<String, dynamic> userData,
  ) async {
    try {
      final createdBy = userData['created_by'] as String?;
      final isFakeData = userData['isFakeData'] as bool?;
      final email = userData['email'] as String?;
      final role = userData['role'] as String?;
      final status = userData['status'] as String?;

      debugPrint('[DYNAMIC_NAV] 🔍 Validation légitimité Admin Compagnie:');
      debugPrint('[DYNAMIC_NAV]   - Email: $email');
      debugPrint('[DYNAMIC_NAV]   - Rôle: $role');
      debugPrint('[DYNAMIC_NAV]   - Statut: $status');
      debugPrint('[DYNAMIC_NAV]   - Créé par: $createdBy');
      debugPrint('[DYNAMIC_NAV]   - isFakeData: $isFakeData');

      // 1. Rejeter UNIQUEMENT les comptes de test auto-créés spécifiques
      if (isFakeData == true &&
          (createdBy == 'test_accounts_service' ||
           createdBy == 'direct_firestore_fix' ||
           createdBy == 'firestore_repair_service')) {
        debugPrint('[DYNAMIC_NAV] ❌ Compte de test auto-créé rejeté');
        return {
          'isLegitimate': false,
          'reason': 'Compte de test automatique non autorisé',
        };
      }

      // 2. Accepter TOUS les comptes avec rôle admin_compagnie et statut actif
      // (sauf ceux explicitement rejetés ci-dessus)
      if (role == 'admin_compagnie' && status == 'actif') {
        debugPrint('[DYNAMIC_NAV] ✅ Compte Admin Compagnie valide accepté');

        // Déterminer la source
        String source = 'unknown';
        if (createdBy == 'super_admin' || createdBy == 'admin_creation_service') {
          source = 'super_admin_creation';
        } else if (createdBy == 'professional_request_approval' ||
                   createdBy == 'demande_professionnelle_approuvee') {
          source = 'professional_request';
        } else if (createdBy == null || createdBy.isEmpty) {
          source = 'legacy_or_manual_creation';
        } else {
          source = 'other_legitimate_creation';
        }

        return {
          'isLegitimate': true,
          'source': source,
          'createdBy': createdBy,
        };
      }

      // 3. Rejeter si le rôle n'est pas admin_compagnie
      if (role != 'admin_compagnie') {
        return {
          'isLegitimate': false,
          'reason': 'Rôle incorrect: $role (attendu: admin_compagnie)',
        };
      }

      // 4. Rejeter si le statut n'est pas actif
      if (status != 'actif') {
        return {
          'isLegitimate': false,
          'reason': 'Statut incorrect: $status (attendu: actif)',
        };
      }

      // 5. Cas par défaut : accepter (pour compatibilité)
      debugPrint('[DYNAMIC_NAV] ⚠️ Validation par défaut - compte accepté');
      return {
        'isLegitimate': true,
        'source': 'default_acceptance',
      };

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur validation légitimité: $e');
      return {
        'isLegitimate': false,
        'reason': 'Erreur technique lors de la validation',
      };
    }
  }

  /// 🏛️ Gestion spécifique Admin Agence
  static Future<Map<String, dynamic>> _handleAdminAgenceRedirection(
    Map<String, dynamic> userData,
    User currentUser,
  ) async {
    try {
      final agenceId = userData['agenceId'] as String?;
      final compagnieId = userData['compagnieId'] as String?;

      if (agenceId == null || compagnieId == null) {
        debugPrint('[DYNAMIC_NAV] ❌ Admin Agence mal configuré');
        return {
          'success': false,
          'error': 'Compte Admin Agence mal configuré',
          'redirectTo': '/login',
        };
      }

      debugPrint('[DYNAMIC_NAV] ✅ Admin Agence valide: $agenceId');

      return {
        'success': true,
        'role': 'admin_agence',
        'redirectTo': '/agence-dashboard',
        'userData': userData,
        'agenceId': agenceId,
        'compagnieId': compagnieId,
      };

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur Admin Agence: $e');
      return {
        'success': false,
        'error': 'Erreur validation Admin Agence: $e',
        'redirectTo': '/login',
      };
    }
  }

  /// 🚀 Navigation automatique avec analyse
  static Future<bool> navigateBasedOnUserRole(BuildContext context) async {
    try {
      debugPrint('[DYNAMIC_NAV] 🚀 Navigation automatique...');

      final analysis = await analyzeUserAndGetRedirection();

      if (!context.mounted) return false;

      if (analysis['success'] == true) {
        final redirectTo = analysis['redirectTo'] as String;
        final role = analysis['role'] as String;

        debugPrint('[DYNAMIC_NAV] ✅ Redirection vers: $redirectTo (rôle: $role)');

        // Navigation avec remplacement
        Navigator.pushReplacementNamed(context, redirectTo);
        return true;

      } else {
        final error = analysis['error'] as String;
        final redirectTo = analysis['redirectTo'] as String;

        debugPrint('[DYNAMIC_NAV] ❌ Erreur: $error');

        // Afficher l'erreur
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ $error'),
            backgroundColor: Colors.red,
          ),
        );

        // Redirection vers login
        Navigator.pushReplacementNamed(context, redirectTo);
        return false;
      }

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur navigation: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur système: $e'),
            backgroundColor: Colors.red,
          ),
        );
        Navigator.pushReplacementNamed(context, '/login');
      }

      return false;
    }
  }

  /// 🔍 Diagnostic complet de l'utilisateur
  static Future<void> diagnosticCurrentUser() async {
    try {
      debugPrint('\n' + '=' * 60);
      debugPrint('🔍 DIAGNOSTIC UTILISATEUR CONNECTÉ');
      debugPrint('=' * 60);

      final analysis = await analyzeUserAndGetRedirection();

      debugPrint('📊 Résultat analyse:');
      analysis.forEach((key, value) {
        debugPrint('   $key: $value');
      });

      debugPrint('=' * 60 + '\n');

    } catch (e) {
      debugPrint('[DYNAMIC_NAV] ❌ Erreur diagnostic: $e');
    }
  }
}
