import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// 🔧 Service de réparation directe Firestore (contourne Firebase Auth)
class DirectFirestoreFix {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔧 Créer directement le document Admin Compagnie avec l'UID connu
  static Future<bool> createAdminCompagnieDocument() async {
    try {
      debugPrint('[DIRECT_FIRESTORE_FIX] 🔧 Création directe document Admin Compagnie...');

      // UID connu depuis les logs
      const knownUid = 'qNx15EZMASOBKpS6OpicoRQduP83';
      
      // 1. Vérifier si le document existe déjà
      final existingDoc = await _firestore
          .collection('users')
          .doc(knownUid)
          .get();

      if (existingDoc.exists) {
        debugPrint('[DIRECT_FIRESTORE_FIX] ⚠️ Document existe déjà');
        final data = existingDoc.data()!;
        debugPrint('[DIRECT_FIRESTORE_FIX] 📋 Données existantes: ${data['role']}');
        return true;
      }

      // 2. <PERSON><PERSON>er d'abord une compagnie de test
      final compagnieId = await _createTestCompagnie();

      // 3. Créer le document utilisateur directement
      final userData = {
        'email': '<EMAIL>',
        'nom': 'Admin',
        'prenom': 'Compagnie',
        'role': 'admin_compagnie',
        'status': 'actif',
        'compagnieId': compagnieId,
        'compagnieNom': 'STAR Assurance',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'direct_firestore_fix',
        'isFakeData': true,
        'last_login': null,
        'password_reset_required': false,
        'phone': '+216 71 123 456',
        'address': '123 Avenue Habib Bourguiba, Tunis',
      };

      await _firestore
          .collection('users')
          .doc(knownUid)
          .set(userData);

      debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Document utilisateur créé avec succès');
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 UID: $knownUid');
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 Email: <EMAIL>');
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 Rôle: admin_compagnie');
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 CompagnieId: $compagnieId');

      return true;

    } catch (e) {
      debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Erreur création: $e');
      return false;
    }
  }

  /// 🏢 Créer une compagnie de test
  static Future<String> _createTestCompagnie() async {
    try {
      const compagnieId = 'star-assurance-test';
      
      // Vérifier si la compagnie existe
      final existingCompagnie = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (existingCompagnie.exists) {
        debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Compagnie existe déjà: $compagnieId');
        return compagnieId;
      }

      // Créer la compagnie
      final compagnieData = {
        'nom': 'STAR Assurance',
        'code': 'STAR',
        'type': 'compagnie_assurance',
        'status': 'actif',
        'adresse': '123 Avenue Habib Bourguiba, Tunis',
        'telephone': '+216 71 123 456',
        'email': '<EMAIL>',
        'site_web': 'www.star-assurance.tn',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'direct_firestore_fix',
        'isFakeData': true,
      };

      await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .set(compagnieData);

      debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Compagnie créée: $compagnieId');
      return compagnieId;

    } catch (e) {
      debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Erreur création compagnie: $e');
      return 'star-assurance-test'; // Retourner un ID par défaut
    }
  }

  /// 🔍 Vérifier l'état du document
  static Future<Map<String, dynamic>> checkDocument() async {
    try {
      const knownUid = 'qNx15EZMASOBKpS6OpicoRQduP83';
      
      final doc = await _firestore
          .collection('users')
          .doc(knownUid)
          .get();

      if (!doc.exists) {
        return {
          'exists': false,
          'uid': knownUid,
          'error': 'Document Firestore manquant',
        };
      }

      final data = doc.data()!;
      return {
        'exists': true,
        'uid': knownUid,
        'data': data,
        'role': data['role'],
        'compagnieId': data['compagnieId'],
        'email': data['email'],
      };

    } catch (e) {
      return {
        'exists': false,
        'error': 'Erreur vérification: $e',
      };
    }
  }

  /// 🔧 Réparation complète avec vérification
  static Future<bool> fullFix() async {
    try {
      debugPrint('[DIRECT_FIRESTORE_FIX] 🚀 Début réparation complète...');

      // 1. Diagnostic initial
      final initialCheck = await checkDocument();
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 État initial: $initialCheck');

      if (initialCheck['exists'] == true) {
        debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Document existe déjà, pas de réparation nécessaire');
        return true;
      }

      // 2. Créer le document
      final created = await createAdminCompagnieDocument();
      
      if (!created) {
        debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Échec création document');
        return false;
      }

      // 3. Vérification finale
      await Future.delayed(const Duration(milliseconds: 2000)); // Attendre la propagation
      final finalCheck = await checkDocument();
      debugPrint('[DIRECT_FIRESTORE_FIX] 📋 État final: $finalCheck');

      if (finalCheck['exists'] == true) {
        debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Réparation complète réussie !');
        debugPrint('[DIRECT_FIRESTORE_FIX] 🎯 Vous pouvez maintenant vous connecter');
        return true;
      } else {
        debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Réparation échouée');
        return false;
      }

    } catch (e) {
      debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Erreur réparation complète: $e');
      return false;
    }
  }

  /// 🧹 Nettoyer les données de test
  static Future<void> cleanTestData() async {
    try {
      debugPrint('[DIRECT_FIRESTORE_FIX] 🧹 Nettoyage données de test...');

      const knownUid = 'qNx15EZMASOBKpS6OpicoRQduP83';

      // Supprimer le document utilisateur
      await _firestore
          .collection('users')
          .doc(knownUid)
          .delete();

      // Supprimer la compagnie de test
      await _firestore
          .collection('compagnies_assurance')
          .doc('star-assurance-test')
          .delete();

      debugPrint('[DIRECT_FIRESTORE_FIX] ✅ Nettoyage terminé');

    } catch (e) {
      debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Erreur nettoyage: $e');
    }
  }

  /// 📊 Diagnostic complet
  static Future<void> fullDiagnostic() async {
    try {
      debugPrint('\n' + '=' * 60);
      debugPrint('🔧 DIAGNOSTIC DIRECT FIRESTORE FIX');
      debugPrint('=' * 60);

      final check = await checkDocument();
      
      debugPrint('📧 Email attendu: <EMAIL>');
      debugPrint('👤 UID connu: qNx15EZMASOBKpS6OpicoRQduP83');
      debugPrint('🗄️ Document existe: ${check['exists'] ? '✅' : '❌'}');
      
      if (check['exists'] == true) {
        final data = check['data'] as Map<String, dynamic>;
        debugPrint('🎭 Rôle: ${data['role']}');
        debugPrint('🏢 CompagnieId: ${data['compagnieId']}');
        debugPrint('📊 Statut: ${data['status']}');
        debugPrint('📧 Email: ${data['email']}');
      } else {
        debugPrint('❌ Erreur: ${check['error']}');
      }
      
      debugPrint('=' * 60 + '\n');

    } catch (e) {
      debugPrint('[DIRECT_FIRESTORE_FIX] ❌ Erreur diagnostic: $e');
    }
  }
}
