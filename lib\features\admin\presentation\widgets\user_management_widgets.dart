import 'package:flutter/material.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/user_management_service_original.dart';
import '../../services/user_role_validation_service_complete.dart';

/// 👤 Carte d'affichage d'un utilisateur
class UserCard extends StatelessWidget {
  final Map<String, dynamic> user;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onResetPassword;

  const UserCard({
    super.key,
    required this.user,
    required this.onEdit,
    required this.onDelete,
    required this.onResetPassword,
  });

  @override
  Widget build(BuildContext context) {
    final role = user['role'] ?? 'inconnu';
    final status = user['status'] ?? 'inconnu';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec nom et actions
            Row(
              children: [
                // Avatar
                CircleAvatar(
                  backgroundColor: UserRoleValidationServiceComplete.getRoleColor(role),
                  child: Icon(
                    UserRoleValidationServiceComplete.getRoleIcon(role),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Nom et email
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${user['prenom']} ${user['nom']}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        user['email'] ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Actions
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEdit();
                        break;
                      case 'reset_password':
                        onResetPassword();
                        break;
                      case 'delete':
                        onDelete();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('Modifier'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reset_password',
                      child: Row(
                        children: [
                          Icon(Icons.lock_reset, size: 18),
                          SizedBox(width: 8),
                          Text('Réinitialiser mot de passe'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Supprimer', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Informations détaillées
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                _buildInfoChip(
                  'Rôle',
                  UserRoleValidationServiceComplete.availableRoles[role] ?? role,
                  UserRoleValidationServiceComplete.getRoleColor(role),
                ),
                _buildInfoChip(
                  'Statut',
                  status,
                  _getStatusColor(status),
                ),
                if (user['compagnieNom'] != null)
                  _buildInfoChip(
                    'Compagnie',
                    user['compagnieNom'],
                    Colors.blue,
                  ),
                if (user['agenceNom'] != null)
                  _buildInfoChip(
                    'Agence',
                    user['agenceNom'],
                    Colors.green,
                  ),
                if (user['phone'] != null)
                  _buildInfoChip(
                    'Téléphone',
                    user['phone'],
                    Colors.grey,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$label: $value',
        style: TextStyle(
          fontSize: 12,
          color: color.withOpacity(0.8),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'actif':
        return Colors.green;
      case 'inactif':
        return Colors.orange;
      case 'suspendu':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// ➕ Dialogue de création d'utilisateur
class CreateUserDialog extends StatefulWidget {
  final List<Map<String, dynamic>> compagnies;
  final List<Map<String, dynamic>> agences;
  final VoidCallback onUserCreated;

  const CreateUserDialog({
    super.key,
    required this.compagnies,
    required this.agences,
    required this.onUserCreated,
  });

  @override
  State<CreateUserDialog> createState() => _CreateUserDialogState();
}

class _CreateUserDialogState extends State<CreateUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  
  String _selectedRole = 'agent';
  String? _selectedCompagnie;
  String? _selectedAgence;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _nomController.dispose();
    _prenomController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Créer un utilisateur'),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Email
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Email requis';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'Email invalide';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Nom et Prénom
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _prenomController,
                        decoration: const InputDecoration(
                          labelText: 'Prénom *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Prénom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: const InputDecoration(
                          labelText: 'Nom *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Nom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Rôle
                DropdownButtonFormField<String>(
                  value: _selectedRole,
                  decoration: const InputDecoration(
                    labelText: 'Rôle *',
                    border: OutlineInputBorder(),
                  ),
                  items: UserRoleValidationServiceComplete.availableRoles.entries
                      .where((entry) => entry.key != 'super_admin') // Exclure super_admin
                      .map((entry) => DropdownMenuItem(
                            value: entry.key,
                            child: Text(entry.value),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedRole = value!;
                      _selectedCompagnie = null;
                      _selectedAgence = null;
                    });
                  },
                ),
                const SizedBox(height: 16),
                
                // Compagnie (si nécessaire)
                if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole))
                  DropdownButtonFormField<String>(
                    value: _selectedCompagnie,
                    decoration: const InputDecoration(
                      labelText: 'Compagnie *',
                      border: OutlineInputBorder(),
                    ),
                    items: widget.compagnies.map((compagnie) => DropdownMenuItem(
                          value: compagnie['id'],
                          child: Text(compagnie['nom']),
                        )).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCompagnie = value;
                        _selectedAgence = null;
                      });
                    },
                    validator: (value) {
                      if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole) && 
                          (value == null || value.isEmpty)) {
                        return 'Compagnie requise';
                      }
                      return null;
                    },
                  ),
                
                if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole))
                  const SizedBox(height: 16),
                
                // Agence (si nécessaire)
                if (UserRoleValidationServiceComplete.requiresAgence(_selectedRole))
                  DropdownButtonFormField<String>(
                    value: _selectedAgence,
                    decoration: const InputDecoration(
                      labelText: 'Agence *',
                      border: OutlineInputBorder(),
                    ),
                    items: widget.agences
                        .where((agence) => _selectedCompagnie == null || 
                               agence['compagnieId'] == _selectedCompagnie)
                        .map((agence) => DropdownMenuItem(
                              value: agence['id'],
                              child: Text(agence['nom']),
                            ))
                        .toList(),
                    onChanged: (value) {
                      setState(() => _selectedAgence = value);
                    },
                    validator: (value) {
                      if (UserRoleValidationServiceComplete.requiresAgence(_selectedRole) && 
                          (value == null || value.isEmpty)) {
                        return 'Agence requise';
                      }
                      return null;
                    },
                  ),
                
                if (UserRoleValidationServiceComplete.requiresAgence(_selectedRole))
                  const SizedBox(height: 16),
                
                // Téléphone
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Téléphone',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Adresse
                TextFormField(
                  controller: _addressController,
                  decoration: const InputDecoration(
                    labelText: 'Adresse',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createUser,
          style: ElevatedButton.styleFrom(
            backgroundColor: ModernTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await UserManagementService.createUser(
        email: _emailController.text.trim(),
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        role: _selectedRole,
        compagnieId: _selectedCompagnie,
        agenceId: _selectedAgence,
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
      );

      if (mounted) {
        if (result['success']) {
          Navigator.pop(context);
          widget.onUserCreated();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Utilisateur créé avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: ${result['error']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// ✏️ Dialogue d'édition d'utilisateur
class EditUserDialog extends StatefulWidget {
  final Map<String, dynamic> user;
  final List<Map<String, dynamic>> compagnies;
  final List<Map<String, dynamic>> agences;
  final VoidCallback onUserUpdated;

  const EditUserDialog({
    super.key,
    required this.user,
    required this.compagnies,
    required this.agences,
    required this.onUserUpdated,
  });

  @override
  State<EditUserDialog> createState() => _EditUserDialogState();
}

class _EditUserDialogState extends State<EditUserDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nomController;
  late final TextEditingController _prenomController;
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  
  late String _selectedRole;
  late String _selectedStatus;
  String? _selectedCompagnie;
  String? _selectedAgence;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nomController = TextEditingController(text: widget.user['nom']);
    _prenomController = TextEditingController(text: widget.user['prenom']);
    _phoneController = TextEditingController(text: widget.user['phone'] ?? '');
    _addressController = TextEditingController(text: widget.user['address'] ?? '');
    
    _selectedRole = widget.user['role'] ?? 'agent';
    _selectedStatus = widget.user['status'] ?? 'actif';
    _selectedCompagnie = widget.user['compagnieId'];
    _selectedAgence = widget.user['agenceId'];
  }

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Modifier ${widget.user['prenom']} ${widget.user['nom']}'),
      content: SizedBox(
        width: 500,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Email (lecture seule)
                TextFormField(
                  initialValue: widget.user['email'],
                  decoration: const InputDecoration(
                    labelText: 'Email',
                    border: OutlineInputBorder(),
                  ),
                  enabled: false,
                ),
                const SizedBox(height: 16),
                
                // Nom et Prénom
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _prenomController,
                        decoration: const InputDecoration(
                          labelText: 'Prénom *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Prénom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: const InputDecoration(
                          labelText: 'Nom *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Nom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Rôle et Statut
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          labelText: 'Rôle *',
                          border: OutlineInputBorder(),
                        ),
                        items: UserRoleValidationServiceComplete.availableRoles.entries
                            .where((entry) => entry.key != 'super_admin')
                            .map((entry) => DropdownMenuItem(
                                  value: entry.key,
                                  child: Text(entry.value),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedRole = value!;
                            if (!UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole)) {
                              _selectedCompagnie = null;
                            }
                            if (!UserRoleValidationServiceComplete.requiresAgence(_selectedRole)) {
                              _selectedAgence = null;
                            }
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedStatus,
                        decoration: const InputDecoration(
                          labelText: 'Statut *',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'actif', child: Text('Actif')),
                          DropdownMenuItem(value: 'inactif', child: Text('Inactif')),
                          DropdownMenuItem(value: 'suspendu', child: Text('Suspendu')),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedStatus = value!);
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Compagnie et Agence (si nécessaires)
                if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole) ||
                    UserRoleValidationServiceComplete.requiresAgence(_selectedRole)) ...[
                  Row(
                    children: [
                      if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole))
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCompagnie,
                            decoration: const InputDecoration(
                              labelText: 'Compagnie *',
                              border: OutlineInputBorder(),
                            ),
                            items: widget.compagnies.map((compagnie) => DropdownMenuItem(
                                  value: compagnie['id'],
                                  child: Text(compagnie['nom']),
                                )).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCompagnie = value;
                                _selectedAgence = null;
                              });
                            },
                            validator: (value) {
                              if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole) && 
                                  (value == null || value.isEmpty)) {
                                return 'Compagnie requise';
                              }
                              return null;
                            },
                          ),
                        ),
                      
                      if (UserRoleValidationServiceComplete.requiresCompagnie(_selectedRole) &&
                          UserRoleValidationServiceComplete.requiresAgence(_selectedRole))
                        const SizedBox(width: 16),
                      
                      if (UserRoleValidationServiceComplete.requiresAgence(_selectedRole))
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedAgence,
                            decoration: const InputDecoration(
                              labelText: 'Agence *',
                              border: OutlineInputBorder(),
                            ),
                            items: widget.agences
                                .where((agence) => _selectedCompagnie == null || 
                                       agence['compagnieId'] == _selectedCompagnie)
                                .map((agence) => DropdownMenuItem(
                                      value: agence['id'],
                                      child: Text(agence['nom']),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              setState(() => _selectedAgence = value);
                            },
                            validator: (value) {
                              if (UserRoleValidationServiceComplete.requiresAgence(_selectedRole) && 
                                  (value == null || value.isEmpty)) {
                                return 'Agence requise';
                              }
                              return null;
                            },
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
                
                // Téléphone
                TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(
                    labelText: 'Téléphone',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Adresse
                TextFormField(
                  controller: _addressController,
                  decoration: const InputDecoration(
                    labelText: 'Adresse',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateUser,
          style: ElevatedButton.styleFrom(
            backgroundColor: ModernTheme.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Mettre à jour'),
        ),
      ],
    );
  }

  Future<void> _updateUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await UserManagementService.updateUser(
        uid: widget.user['uid'],
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        role: _selectedRole,
        status: _selectedStatus,
        compagnieId: _selectedCompagnie,
        agenceId: _selectedAgence,
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
      );

      if (mounted) {
        if (result['success']) {
          Navigator.pop(context);
          widget.onUserUpdated();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Utilisateur mis à jour avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: ${result['error']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
