import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/services/init_test_data_service.dart';
import '../../models/professional_account_request.dart';
import '../../../../core/config/app_router.dart';
import 'professional_request_success_screen.dart';
import '../../../../test_firestore_screen.dart';

/// 👥 Écran de sélection du type d'utilisateur moderne
class UserTypeSelectionScreen extends StatelessWidget {
  const UserTypeSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF0F0F23), // Bleu très sombre
              Color(0xFF1A1A2E), // Bleu sombre
              Color(0xFF16213E), // Bleu moyen
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // En-tête moderne et compact
              _buildModernHeader(context),

              // Contenu principal avec design moderne
              Expanded(
                child: _buildModernContent(context),
              ),

              // Footer moderne et compact
              _buildModernFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 📱 En-tête avec logo et titre
  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Logo de l'application
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const Icon(
              Icons.security,
              size: 40,
              color: AppTheme.primaryColor,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Titre de l'application
          Text(
            AppConstants.appName,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Sous-titre
          Text(
            'Application d\'assurance automobile digitalisée',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 🎯 Titre de sélection
  Widget _buildSelectionTitle(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choisissez votre profil',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Sélectionnez le type de compte qui correspond à votre profil',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 👥 Options de types d'utilisateurs - Version optimisée
  Widget _buildUserTypeOptions(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            children: [
              // Conducteur/Client
              _buildUserTypeCard(
                context,
                icon: Icons.directions_car,
                title: 'Conducteur / Client',
                subtitle: 'Déclarer un sinistre, consulter mes contrats',
                color: AppTheme.driverColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  AppRouter.login,
                  arguments: {'userType': 'driver'},
                ),
              ),

              const SizedBox(height: 12),

              // Agent d'assurance
              _buildUserTypeCard(
                context,
                icon: Icons.business_center,
                title: 'Agent d\'Assurance',
                subtitle: 'Gérer les contrats et les clients',
                color: AppTheme.agentColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  AppRouter.login,
                  arguments: {'userType': 'agent'},
                ),
              ),

              const SizedBox(height: 12),

              // Expert automobile
              _buildUserTypeCard(
                context,
                icon: Icons.engineering,
                title: 'Expert Automobile',
                subtitle: 'Évaluer les sinistres et rédiger des rapports',
                color: AppTheme.expertColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  AppRouter.login,
                  arguments: {'userType': 'expert'},
                ),
              ),

              const SizedBox(height: 12),

              // Administrateur
              _buildUserTypeCard(
                context,
                icon: Icons.admin_panel_settings,
                title: 'Administrateur',
                subtitle: 'Gérer le système et les utilisateurs',
                color: AppTheme.adminColor,
                onTap: () => Navigator.pushNamed(
                  context,
                  AppRouter.adminCompagnieLogin,
                ),
              ),

              const SizedBox(height: 16),

              // 📋 Bouton demande de compte professionnel
              _buildProfessionalRequestButton(context),

              const SizedBox(height: 16),

              // 🧪 Bouton de test direct
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.pushNamed(context, '/test-formulaire'),
                  icon: const Icon(Icons.bug_report),
                  label: const Text('🧪 TEST DIRECT FORMULAIRE'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // 🔥 Bouton de test navigation
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.pushNamed(context, '/test-navigation'),
                  icon: const Icon(Icons.navigation),
                  label: const Text('🔥 TEST NAVIGATION'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),

              // Espace final pour éviter l'overflow
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  /// 📋 Bouton pour demander un compte professionnel
  Widget _buildProfessionalRequestButton(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: OutlinedButton.icon(
        onPressed: () {
          print('🔥 BOUTON HAUT CLIQUÉ - Navigation vers formulaire professionnel');
          Navigator.pushNamed(context, AppRouter.professionalAccountRequest);
        },
        icon: const Icon(Icons.business_center_outlined),
        label: const Text('Demander un Compte Professionnel'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          side: BorderSide(color: AppTheme.primaryColor, width: 2),
          foregroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// 🎴 Carte de type d'utilisateur
  Widget _buildUserTypeCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              // Icône
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: color,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Texte
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Flèche
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppTheme.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🦶 Footer avec informations
  Widget _buildFooter(BuildContext context) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 16),
        
        // Boutons d'aide - Version responsive
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: TextButton.icon(
                  onPressed: () {
                    // Ouvrir l'aide
                    _showHelpDialog(context);
                  },
                  icon: const Icon(Icons.help_outline, size: 18),
                  label: const Text(
                    'Aide',
                    style: TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: TextButton.icon(
                  onPressed: () {
                    // Ouvrir les conditions
                    _showTermsDialog(context);
                  },
                  icon: const Icon(Icons.description_outlined, size: 18),
                  label: const Text(
                    'Conditions',
                    style: TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4),
                child: TextButton.icon(
                  onPressed: () {
                    // Contacter le support
                    _showContactDialog(context);
                  },
                  icon: const Icon(Icons.contact_support_outlined, size: 18),
                  label: const Text(
                    'Contact',
                    style: TextStyle(fontSize: 12),
                    overflow: TextOverflow.ellipsis,
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  ),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Version de l'application
        Text(
          'Version ${AppConstants.appVersion}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.textHint,
          ),
        ),

        const SizedBox(height: 8),

        // 🔐 Accès Super Admin (discret)
        _buildSuperAdminAccess(context),
      ],
    );
  }

  /// ❓ Dialog d'aide
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aide'),
        content: const Text(
          'Choisissez votre profil selon votre rôle :\n\n'
          '• Conducteur : Si vous êtes assuré et souhaitez déclarer un sinistre\n'
          '• Agent : Si vous travaillez pour une compagnie d\'assurance\n'
          '• Expert : Si vous êtes expert automobile\n'
          '• Administrateur : Si vous gérez le système',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Compris'),
          ),
        ],
      ),
    );
  }

  /// 📄 Dialog des conditions
  void _showTermsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conditions d\'utilisation'),
        content: const Text(
          'En utilisant cette application, vous acceptez nos conditions d\'utilisation et notre politique de confidentialité.\n\n'
          'Cette application est destinée aux professionnels de l\'assurance et aux assurés en Tunisie.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📞 Dialog de contact
  void _showContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Nous contacter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Pour toute question ou assistance :'),
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(Icons.email, size: 20),
                const SizedBox(width: 8),
                Text(AppConstants.supportEmail),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.web, size: 20),
                const SizedBox(width: 8),
                Text(AppConstants.helpUrl),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 🔐 Accès Super Admin discret
  Widget _buildSuperAdminAccess(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Accès Super Admin
        GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, '/super-admin-login');
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            child: Text(
              '🔐',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textHint.withValues(alpha: 0.5),
                fontSize: 12,
              ),
            ),
          ),
        ),

        const SizedBox(width: 16),

        // Interface de test (développement uniquement)
        GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const TestFirestoreScreen(),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
            child: Text(
              '🧹',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textHint.withValues(alpha: 0.5),
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // ========== NOUVELLES MÉTHODES MODERNES ==========

  /// 🎨 En-tête moderne et élégant
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Logo moderne avec effet glassmorphism
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Icon(
              Icons.security_rounded,
              size: 28,
              color: Colors.white,
            ),
          ),

          const SizedBox(width: 16),

          // Titre et sous-titre
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Constat Tunisie',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w800,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Assurance digitalisée',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Badge moderne
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Text(
              'V2.0',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 📱 Contenu principal moderne
  Widget _buildModernContent(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 30,
            offset: const Offset(0, 15),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: Column(
          children: [
            // Titre de sélection moderne
            _buildModernSelectionTitle(context),

            // Options de profils modernes
            Expanded(
              child: _buildModernProfileOptions(context),
            ),
          ],
        ),
      ),
    );
  }

  /// 🎯 Titre de sélection moderne
  Widget _buildModernSelectionTitle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF667EEA).withValues(alpha: 0.1),
            const Color(0xFF764BA2).withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Choisissez votre profil',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF2D3748),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Text(
              'Sélectionnez le type de compte qui correspond à votre rôle',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 👥 Options de profils modernes
  Widget _buildModernProfileOptions(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(20, 16, 20, 8), // Réduire le padding
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight - 16, // Réserver de l'espace
            ),
            child: Column(
              children: [
                // Conducteur/Client
                _buildModernProfileCard(
                  context,
                  icon: Icons.directions_car_rounded,
                  title: 'Conducteur',
                  subtitle: 'Gérer les contrats et déclarer les sinistres',
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                  ),
                  onTap: () => Navigator.pushNamed(
                    context,
                    AppRouter.login,
                    arguments: {'userType': 'driver'},
                  ),
                ),

                const SizedBox(height: 12), // Réduire l'espacement

                // Agent d'assurance
                _buildModernProfileCard(
                  context,
                  icon: Icons.business_center_rounded,
                  title: 'Agent d\'Assurance',
                  subtitle: 'Gérer les contrats et les clients',
                  gradient: const LinearGradient(
                    colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                  ),
                  onTap: () => Navigator.pushNamed(
                    context,
                    AppRouter.login,
                    arguments: {'userType': 'agent'},
                  ),
                ),

                const SizedBox(height: 12), // Réduire l'espacement

                // Expert automobile
                _buildModernProfileCard(
                  context,
                  icon: Icons.engineering_rounded,
                  title: 'Expert Automobile',
                  subtitle: 'Évaluer les sinistres et rédiger des rapports',
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFF9800), Color(0xFFFF8F00)],
                  ),
                  onTap: () => Navigator.pushNamed(
                    context,
                    AppRouter.login,
                    arguments: {'userType': 'expert'},
                  ),
                ),

                const SizedBox(height: 12), // Réduire l'espacement

                // Administrateur
                _buildModernProfileCard(
                  context,
                  icon: Icons.admin_panel_settings_rounded,
                  title: 'Administrateur',
                  subtitle: 'Gérer le système et les utilisateurs',
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9C27B0), Color(0xFF8E24AA)],
                  ),
                  onTap: () => Navigator.pushNamed(
                    context,
                    AppRouter.adminCompagnieLogin,
                  ),
                ),

                const SizedBox(height: 16), // Réduire l'espacement

                // Bouton demande de compte professionnel moderne
                _buildModernProfessionalButton(context),

                const SizedBox(height: 8), // Espacement final minimal
              ],
            ),
          ),
        );
      },
    );
  }

  /// 🎴 Carte de profil moderne
  Widget _buildModernProfileCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16), // Réduire de 20 à 16
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Icône avec gradient (plus compacte)
                Container(
                  width: 48, // Réduire de 56 à 48
                  height: 48, // Réduire de 56 à 48
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(14), // Réduire de 16 à 14
                    boxShadow: [
                      BoxShadow(
                        color: gradient.colors.first.withValues(alpha: 0.3),
                        blurRadius: 6, // Réduire de 8 à 6
                        offset: const Offset(0, 3), // Réduire de 4 à 3
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    size: 24, // Réduire de 28 à 24
                    color: Colors.white,
                  ),
                ),

                const SizedBox(width: 14), // Réduire de 16 à 14

                // Texte
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 15, // Réduire de 16 à 15
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF2D3748),
                        ),
                      ),
                      const SizedBox(height: 3), // Réduire de 4 à 3
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12, // Réduire de 13 à 12
                          color: Colors.grey[600],
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),

                // Flèche moderne (plus compacte)
                Container(
                  width: 28, // Réduire de 32 à 28
                  height: 28, // Réduire de 32 à 28
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(7), // Réduire de 8 à 7
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: 14, // Réduire de 16 à 14
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 📋 Bouton demande de compte professionnel moderne
  Widget _buildModernProfessionalButton(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF667EEA),
          width: 2,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            print('🔥 BOUTON CLIQUÉ - Navigation vers formulaire professionnel');
            Navigator.pushNamed(context, AppRouter.professionalAccountRequest);
          },
          borderRadius: BorderRadius.circular(14),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF667EEA).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.business_center_outlined,
                    size: 20,
                    color: Color(0xFF667EEA),
                  ),
                ),
                const SizedBox(width: 12),
                const Flexible(
                  child: Text(
                    'Demander un Compte Professionnel',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF667EEA),
                    ),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 🔄 Réinitialiser l'onboarding (temporaire pour les tests)
  void _resetOnboarding(BuildContext context) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('onboarding_completed', false);
      if (context.mounted) {
        Navigator.pushReplacementNamed(context, AppRouter.onboarding);
      }
    } catch (e) {
      debugPrint('[USER_TYPE_SELECTION] Erreur lors de la réinitialisation de l\'onboarding: $e');
    }
  }

  /// 🧪 Initialiser les données de test (temporaire)
  Future<void> _initTestData(BuildContext context) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Initialisation des données de test...'),
          backgroundColor: Colors.blue,
        ),
      );

      // Attendre un peu pour s'assurer que Firebase est prêt
      await Future.delayed(const Duration(seconds: 1));

      await InitTestDataService.initAllTestData();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Données de test initialisées avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// ✅ Tester l'écran de succès (temporaire pour les tests)
  void _testSuccessScreen(BuildContext context) {
    final testRequest = ProfessionalAccountRequest(
      id: 'test-123',
      fullName: 'Sami Kacem',
      email: '<EMAIL>',
      phone: '+216 20 123 456',
      role: 'agent_agence',
      companyName: 'STAR',
      agencyName: 'STAR Ariana',
      governorate: 'Ariana',
      licenseNumber: 'AG-001',
      experience: '5 ans',
      submittedAt: DateTime.now(),
    );

    Navigator.pushNamed(
      context,
      AppRouter.professionalRequestSuccess,
      arguments: testRequest,
    );
  }

  /// 🦶 Footer moderne
  Widget _buildModernFooter(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Version
          Text(
            'Version ${AppConstants.appVersion}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.6),
            ),
          ),

          // Accès admin discret
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pushNamed(context, '/super-admin-login'),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '🔐',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () => Navigator.pushNamed(context, '/clean-firestore'),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '🧹',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Bouton pour réinitialiser l'onboarding (temporaire)
              GestureDetector(
                onTap: () => _resetOnboarding(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '🔄',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Bouton pour initialiser les données de test (temporaire)
              GestureDetector(
                onTap: () => _initTestData(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '🧪',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Bouton pour tester l'écran de succès (temporaire)
              GestureDetector(
                onTap: () => _testSuccessScreen(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Text(
                    '✅',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
