import 'package:flutter/material.dart';

import '../enums/app_enums.dart';
import '../../features/splash/presentation/screens/splash_screen.dart';
import '../../features/onboarding/presentation/screens/onboarding_screen.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/user_type_selection_screen.dart';
import '../../features/driver/presentation/screens/driver_dashboard.dart';
import '../../features/auth/presentation/screens/elegant_professional_request_screen.dart';
import '../../features/auth/presentation/screens/professional_request_success_screen.dart';
import '../../features/auth/models/professional_account_request.dart';
import '../../features/admin/presentation/screens/super_admin_login_screen.dart';
import '../../features/admin/presentation/screens/super_admin_dashboard_screen.dart';
import '../../features/admin/presentation/screens/users_management_screen.dart';
import '../../features/admin/presentation/screens/professional_requests_management_screen.dart';
import '../../features/admin/presentation/screens/agences_management_screen.dart';
import '../../features/admin/presentation/screens/compagnies_management_screen.dart';
import '../../features/admin/presentation/screens/audit_logs_screen.dart';
import '../../features/agent/presentation/screens/agent_dashboard.dart';
import '../../features/expert/presentation/screens/expert_dashboard.dart';
import '../../features/admin_compagnie/presentation/screens/compagnie_dashboard_screen.dart';
import '../../features/admin_compagnie/presentation/screens/agences_management_screen.dart' as compagnie_agences;
import '../../features/admin_compagnie/presentation/screens/employes_management_screen.dart' as compagnie_employes;
import '../../features/auth/presentation/screens/admin_compagnie_login_screen.dart';

import '../../features/admin/utils/clean_firestore.dart';

/// 🛣️ Configuration des routes de l'application
class AppRouter {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String userTypeSelection = '/user-type-selection';
  static const String login = '/login';
  static const String professionalAccountRequest = '/professional-account-request';
  static const String professionalRequestSuccess = '/professional-request-success';
  static const String superAdminLogin = '/super-admin-login';
  static const String superAdminDashboard = '/super-admin-dashboard';
  static const String compagnieDashboard = '/compagnie-dashboard';
  static const String compagnieAgences = '/compagnie-agences';
  static const String compagnieEmployes = '/compagnie-employes';
  static const String adminCompagnieLogin = '/admin-compagnie-login';
  static const String cleanFirestore = '/clean-firestore';

  // Routes Admin
  static const String adminDashboard = '/admin-dashboard';
  static const String companyAdminDashboard = '/company-admin-dashboard';
  static const String agencyAdminDashboard = '/agency-admin-dashboard';
  static const String adminUsers = '/admin/users';
  static const String adminProfessionalRequests = '/admin/professional-requests';
  static const String adminAgences = '/admin/agences';
  static const String adminCompagnies = '/admin/compagnies';
  static const String adminAuditLogs = '/admin/audit-logs';

  // Routes Driver
  static const String driverDashboard = '/driver';
  static const String driverProfile = '/driver/profile';
  static const String driverVehicles = '/driver/vehicles';
  static const String driverContracts = '/driver/contracts';
  static const String driverClaims = '/driver/claims';

  // Routes Agent & Expert
  static const String agentDashboard = '/agent-dashboard';
  static const String expertDashboard = '/expert-dashboard';
  
  // Routes partagées
  static const String notifications = '/notifications';
  static const String profile = '/profile';
  static const String settings = '/settings';

  /// 🧭 Générateur de routes
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
        );

      case onboarding:
        return MaterialPageRoute(
          builder: (_) => const OnboardingScreen(),
        );

      case userTypeSelection:
        return MaterialPageRoute(
          builder: (_) => const UserTypeSelectionScreen(),
        );
      
      case login:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => LoginScreen(
            userType: args?['userType'],
          ),
        );
      
      case driverDashboard:
        return MaterialPageRoute(
          builder: (_) => const DriverDashboard(),
        );

      case professionalAccountRequest:
        return MaterialPageRoute(
          builder: (_) => const ElegantProfessionalRequestScreen(),
        );

      case professionalRequestSuccess:
        final request = settings.arguments as ProfessionalAccountRequest?;
        if (request == null) {
          return MaterialPageRoute(
            builder: (_) => const Scaffold(
              body: Center(
                child: Text('Erreur: Données de demande manquantes'),
              ),
            ),
          );
        }
        return MaterialPageRoute(
          builder: (_) => ProfessionalRequestSuccessScreen(request: request),
        );

      case superAdminLogin:
        return MaterialPageRoute(
          builder: (_) => const SuperAdminLoginScreen(),
        );

      case superAdminDashboard:
        return MaterialPageRoute(
          builder: (_) => const SuperAdminDashboardScreen(),
        );

      case companyAdminDashboard:
      case compagnieDashboard:
        return MaterialPageRoute(
          builder: (_) => const CompagnieDashboardScreen(),
        );

      case compagnieAgences:
        return MaterialPageRoute(
          builder: (_) => const compagnie_agences.AgencesManagementScreen(),
        );

      case compagnieEmployes:
        return MaterialPageRoute(
          builder: (_) => const compagnie_employes.EmployesManagementScreen(),
        );

      case adminCompagnieLogin:
        return MaterialPageRoute(
          builder: (_) => const AdminCompagnieLoginScreen(),
        );

      case adminUsers:
        return MaterialPageRoute(
          builder: (_) => const UsersManagementScreen(),
        );

      case adminProfessionalRequests:
        return MaterialPageRoute(
          builder: (_) => const ProfessionalRequestsManagementScreen(),
        );

      case adminAgences:
        return MaterialPageRoute(
          builder: (_) => const AgencesManagementScreen(),
        );

      case adminCompagnies:
        return MaterialPageRoute(
          builder: (_) => const CompagniesManagementScreen(),
        );

      case adminAuditLogs:
        return MaterialPageRoute(
          builder: (_) => const AuditLogsScreen(),
        );

      case agentDashboard:
        return MaterialPageRoute(
          builder: (_) => const AgentDashboard(),
        );

      case expertDashboard:
        return MaterialPageRoute(
          builder: (_) => const ExpertDashboard(),
        );



      case cleanFirestore:
        return MaterialPageRoute(
          builder: (_) => const CleanFirestoreWidget(),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundScreen(),
        );
    }
  }

  /// 🧭 Navigation vers le dashboard selon le rôle
  static String getDashboardRoute(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return superAdminDashboard;
      case UserRole.companyAdmin:
        return companyAdminDashboard;
      case UserRole.agencyAdmin:
        return agencyAdminDashboard;
      case UserRole.agent:
        return agentDashboard;
      case UserRole.driver:
        return driverDashboard;
      case UserRole.expert:
        return expertDashboard;
    }
  }
}

/// 📄 Écran pour les pages non trouvées
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page non trouvée'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16),
            Text(
              'Page non trouvée',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'La page que vous cherchez n\'existe pas.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
