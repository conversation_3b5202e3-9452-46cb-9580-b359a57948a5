import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// 🔄 Service de redirection dynamique pour les Admin Compagnies
/// 
/// Ce service gère la redirection automatique des admin_compagnie vers
/// leur dashboard spécifique basé sur leur compagnieId
class CompanyRedirectService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 🎯 Obtenir la route de redirection pour un admin compagnie
  static Future<String?> getCompanyDashboardRoute(String userId) async {
    try {
      debugPrint('[COMPANY_REDIRECT] 🔍 Recherche route pour utilisateur: $userId');

      final userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();

      if (!userDoc.exists) {
        debugPrint('[COMPANY_REDIRECT] ❌ Utilisateur non trouvé');
        return null;
      }

      final userData = userDoc.data()!;
      final role = userData['role'] as String?;
      final compagnieId = userData['compagnieId'] as String?;

      if (role != 'admin_compagnie') {
        debugPrint('[COMPANY_REDIRECT] ⚠️ Utilisateur n\'est pas admin_compagnie: $role');
        return null;
      }

      if (compagnieId == null || compagnieId.isEmpty) {
        debugPrint('[COMPANY_REDIRECT] ❌ CompagnieId manquant pour admin_compagnie');
        return null;
      }

      // Vérifier que la compagnie existe
      final compagnieDoc = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (!compagnieDoc.exists) {
        debugPrint('[COMPANY_REDIRECT] ❌ Compagnie non trouvée: $compagnieId');
        return null;
      }

      final compagnieData = compagnieDoc.data()!;
      final compagnieNom = compagnieData['nom'] as String;

      debugPrint('[COMPANY_REDIRECT] ✅ Redirection vers dashboard: $compagnieNom ($compagnieId)');

      // Retourner la route spécifique à la compagnie
      return '/compagnie-dashboard/$compagnieId';

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur redirection: $e');
      return null;
    }
  }

  /// 🔐 Vérifier si l'utilisateur actuel peut accéder à une compagnie
  static Future<bool> canAccessCompany(String compagnieId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('[COMPANY_REDIRECT] ❌ Aucun utilisateur connecté');
        return false;
      }

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        debugPrint('[COMPANY_REDIRECT] ❌ Document utilisateur non trouvé');
        return false;
      }

      final userData = userDoc.data()!;
      final userRole = userData['role'] as String?;
      final userCompagnieId = userData['compagnieId'] as String?;

      // Super admin peut accéder à toutes les compagnies
      if (userRole == 'super_admin') {
        debugPrint('[COMPANY_REDIRECT] ✅ Accès Super Admin autorisé');
        return true;
      }

      // Admin compagnie ne peut accéder qu'à sa propre compagnie
      if (userRole == 'admin_compagnie') {
        final hasAccess = userCompagnieId == compagnieId;
        debugPrint('[COMPANY_REDIRECT] ${hasAccess ? "✅" : "❌"} Accès admin_compagnie: $userCompagnieId vs $compagnieId');
        return hasAccess;
      }

      debugPrint('[COMPANY_REDIRECT] ❌ Rôle non autorisé: $userRole');
      return false;

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur vérification accès: $e');
      return false;
    }
  }

  /// 📊 Obtenir les informations de la compagnie de l'utilisateur actuel
  static Future<Map<String, dynamic>?> getCurrentUserCompanyInfo() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return null;

      final userData = userDoc.data()!;
      final compagnieId = userData['compagnieId'] as String?;

      if (compagnieId == null) return null;

      final compagnieDoc = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (!compagnieDoc.exists) return null;

      final compagnieData = compagnieDoc.data()!;

      return {
        'id': compagnieId,
        'nom': compagnieData['nom'],
        'code': compagnieData['code'],
        'logo': compagnieData['logo'],
        'couleur': compagnieData['couleur'],
        'userRole': userData['role'],
        'userEmail': userData['email'],
        'userNom': userData['nom'],
        'userPrenom': userData['prenom'],
      };

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur récupération info compagnie: $e');
      return null;
    }
  }

  /// 🛡️ Middleware de sécurité pour les routes compagnie
  static Future<bool> validateCompanyAccess(String compagnieId) async {
    try {
      // Vérifier l'accès
      final hasAccess = await canAccessCompany(compagnieId);
      
      if (!hasAccess) {
        debugPrint('[COMPANY_REDIRECT] 🚫 ACCÈS REFUSÉ à la compagnie: $compagnieId');
        
        // Log de sécurité
        await _logSecurityViolation(
          action: 'unauthorized_company_access_attempt',
          compagnieId: compagnieId,
          reason: 'Tentative d\'accès non autorisé à une compagnie',
        );
        
        return false;
      }

      debugPrint('[COMPANY_REDIRECT] ✅ Accès autorisé à la compagnie: $compagnieId');
      return true;

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur validation accès: $e');
      return false;
    }
  }

  /// 📝 Logger les violations de sécurité
  static Future<void> _logSecurityViolation({
    required String action,
    required String compagnieId,
    required String reason,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      
      await _firestore.collection('security_logs').add({
        'action': action,
        'compagnieId': compagnieId,
        'reason': reason,
        'userId': currentUser?.uid,
        'userEmail': currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
        'severity': 'HIGH',
        'type': 'company_access_violation',
      });

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur log sécurité: $e');
    }
  }

  /// 🔄 Rediriger automatiquement selon le rôle
  static Future<String> getRedirectRouteForUser() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return '/login';
      }

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        return '/login';
      }

      final userData = userDoc.data()!;
      final role = userData['role'] as String?;

      switch (role) {
        case 'super_admin':
          return '/admin-dashboard';
        
        case 'admin_compagnie':
          final compagnieId = userData['compagnieId'] as String?;
          if (compagnieId != null) {
            return '/compagnie-dashboard/$compagnieId';
          }
          return '/login'; // Compagnie manquante
        
        case 'admin_agence':
          final agenceId = userData['agenceId'] as String?;
          if (agenceId != null) {
            return '/agence-dashboard/$agenceId';
          }
          return '/login'; // Agence manquante
        
        case 'agent':
          return '/agent-dashboard';
        
        case 'expert':
          return '/expert-dashboard';
        
        case 'conducteur':
          return '/conducteur-dashboard';
        
        default:
          debugPrint('[COMPANY_REDIRECT] ⚠️ Rôle inconnu: $role');
          return '/login';
      }

    } catch (e) {
      debugPrint('[COMPANY_REDIRECT] ❌ Erreur redirection utilisateur: $e');
      return '/login';
    }
  }
}
