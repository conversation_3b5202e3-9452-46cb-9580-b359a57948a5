import 'package:flutter/material.dart';
import '../../services/user_management_service_original.dart';

/// 📝 Formulaire de création d'utilisateur (Original fonctionnel)
class UserCreateFormWidget extends StatefulWidget {
  final List<Map<String, dynamic>> compagnies;
  final List<Map<String, dynamic>> agences;
  final VoidCallback onUserCreated;

  const UserCreateFormWidget({
    super.key,
    required this.compagnies,
    required this.agences,
    required this.onUserCreated,
  });

  @override
  State<UserCreateFormWidget> createState() => _UserCreateFormWidgetState();
}

class _UserCreateFormWidgetState extends State<UserCreateFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();

  String? _selectedRole;
  String? _selectedCompagnie;
  String? _selectedAgence;
  bool _isLoading = false;

  final List<Map<String, String>> _roles = [
    {'value': 'admin_compagnie', 'label': 'Administrateur de Compagnie'},
    {'value': 'admin_agence', 'label': 'Administrateur d\'Agence'},
    {'value': 'agent_agence', 'label': 'Agent d\'Agence'},
    {'value': 'expert_auto', 'label': 'Expert Automobile'},
    {'value': 'conducteur', 'label': 'Conducteur'},
  ];

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _nomController.dispose();
    _prenomController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// 📝 Créer l'utilisateur
  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      Map<String, dynamic> result;

      // Créer selon le rôle sélectionné
      switch (_selectedRole) {
        case 'admin_compagnie':
          if (_selectedCompagnie == null) {
            throw Exception('Compagnie requise pour Admin Compagnie');
          }
          result = await UserManagementService.createUser(
            email: _emailController.text.trim(),
            nom: _nomController.text.trim(),
            prenom: _prenomController.text.trim(),
            role: 'admin_compagnie',
            compagnieId: _selectedCompagnie!,
            phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
            address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
          );
          break;

        case 'admin_agence':
          if (_selectedCompagnie == null || _selectedAgence == null) {
            throw Exception('Compagnie et Agence requises pour Admin Agence');
          }
          result = await UserManagementService.createUser(
            email: _emailController.text.trim(),
            nom: _nomController.text.trim(),
            prenom: _prenomController.text.trim(),
            role: 'admin_agence',
            compagnieId: _selectedCompagnie!,
            agenceId: _selectedAgence!,
            phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
            address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
          );
          break;

        case 'agent_agence':
          if (_selectedCompagnie == null || _selectedAgence == null) {
            throw Exception('Compagnie et Agence requises pour Agent');
          }
          result = await UserManagementService.createUser(
            email: _emailController.text.trim(),
            nom: _nomController.text.trim(),
            prenom: _prenomController.text.trim(),
            role: 'agent',
            compagnieId: _selectedCompagnie!,
            agenceId: _selectedAgence!,
            phone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
            address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
          );
          break;

        default:
          throw Exception('Type d\'utilisateur non supporté: $_selectedRole');
      }

      if (result['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Utilisateur créé avec succès !'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onUserCreated();
      } else {
        throw Exception(result['error'] ?? 'Erreur inconnue');
      }

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🔍 Obtenir les agences filtrées par compagnie
  List<Map<String, dynamic>> _getFilteredAgences() {
    if (_selectedCompagnie == null) return [];
    return widget.agences
        .where((agence) => agence['compagnieId'] == _selectedCompagnie)
        .toList();
  }

  /// 🎨 Vérifier si le rôle nécessite une compagnie
  bool _requiresCompagnie() {
    return _selectedRole != null && _selectedRole != 'conducteur';
  }

  /// 🎨 Vérifier si le rôle nécessite une agence
  bool _requiresAgence() {
    return _selectedRole == 'admin_agence' || _selectedRole == 'agent_agence';
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Titre
            const Text(
              'Créer un Nouvel Utilisateur',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Informations personnelles
            const Text(
              'Informations Personnelles',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _prenomController,
                    decoration: const InputDecoration(
                      labelText: 'Prénom *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Prénom requis';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _nomController,
                    decoration: const InputDecoration(
                      labelText: 'Nom *',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Nom requis';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email *',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Email requis';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return 'Email invalide';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'Mot de passe *',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Mot de passe requis';
                }
                if (value.length < 6) {
                  return 'Mot de passe trop court (min 6 caractères)';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _phoneController,
                    decoration: const InputDecoration(
                      labelText: 'Téléphone',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _addressController,
                    decoration: const InputDecoration(
                      labelText: 'Adresse',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Rôle et affectations
            const Text(
              'Rôle et Affectations',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            
            const SizedBox(height: 16),
            
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: 'Rôle *',
                border: OutlineInputBorder(),
              ),
              value: _selectedRole,
              items: _roles.map((role) => DropdownMenuItem(
                value: role['value'],
                child: Text(role['label']!),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedRole = value;
                  _selectedCompagnie = null;
                  _selectedAgence = null;
                });
              },
              validator: (value) {
                if (value == null) {
                  return 'Rôle requis';
                }
                return null;
              },
            ),
            
            if (_requiresCompagnie()) ...[
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Compagnie *',
                  border: OutlineInputBorder(),
                ),
                value: _selectedCompagnie,
                items: widget.compagnies.map((compagnie) => DropdownMenuItem<String>(
                  value: compagnie['id'] as String,
                  child: Text(compagnie['nom'] ?? 'Sans nom'),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCompagnie = value;
                    _selectedAgence = null;
                  });
                },
                validator: (value) {
                  if (_requiresCompagnie() && value == null) {
                    return 'Compagnie requise';
                  }
                  return null;
                },
              ),
            ],
            
            if (_requiresAgence()) ...[
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Agence *',
                  border: OutlineInputBorder(),
                ),
                value: _selectedAgence,
                items: _getFilteredAgences().map((agence) => DropdownMenuItem<String>(
                  value: agence['id'] as String,
                  child: Text(agence['nom'] ?? 'Sans nom'),
                )).toList(),
                onChanged: (value) {
                  setState(() => _selectedAgence = value);
                },
                validator: (value) {
                  if (_requiresAgence() && value == null) {
                    return 'Agence requise';
                  }
                  return null;
                },
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Boutons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('Annuler'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _createUser,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Créer'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
