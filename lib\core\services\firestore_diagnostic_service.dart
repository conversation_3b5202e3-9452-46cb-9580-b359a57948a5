import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// 🔍 Service de diagnostic Firestore
/// 
/// Identifie pourquoi Firestore semble indisponible
class FirestoreDiagnosticService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔍 Effectuer un diagnostic complet
  static Future<Map<String, dynamic>> performFullDiagnostic() async {
    debugPrint('[FIRESTORE_DIAGNOSTIC] 🔍 Début diagnostic complet...');
    
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'recommendations': <String>[],
      'overall_status': 'unknown',
    };

    // Test 1: Connectivité Internet
    await _testInternetConnectivity(results);
    
    // Test 2: Authentification Firebase
    await _testFirebaseAuth(results);
    
    // Test 3: Configuration Firestore
    await _testFirestoreConfig(results);
    
    // Test 4: Règles de sécurité
    await _testSecurityRules(results);
    
    // Test 5: Lecture/Écriture simple
    await _testBasicOperations(results);
    
    // Test 6: Latence réseau
    await _testNetworkLatency(results);

    // Analyser les résultats
    _analyzeResults(results);

    debugPrint('[FIRESTORE_DIAGNOSTIC] 📊 Diagnostic terminé');
    return results;
  }

  /// 🌐 Test connectivité Internet
  static Future<void> _testInternetConnectivity(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🌐 Test connectivité Internet...');
      
      final connectivityResult = await Connectivity().checkConnectivity();
      final hasConnection = connectivityResult != ConnectivityResult.none;
      
      results['tests']['internet'] = {
        'status': hasConnection ? 'success' : 'failed',
        'connection_type': connectivityResult.toString(),
        'message': hasConnection ? 'Connexion Internet OK' : 'Pas de connexion Internet',
      };
      
      if (!hasConnection) {
        results['recommendations'].add('🌐 Vérifier la connexion Internet');
      }
      
    } catch (e) {
      results['tests']['internet'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Erreur test connectivité',
      };
    }
  }

  /// 🔐 Test authentification Firebase
  static Future<void> _testFirebaseAuth(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🔐 Test authentification Firebase...');
      
      final currentUser = _auth.currentUser;
      final isAuthenticated = currentUser != null;
      
      results['tests']['auth'] = {
        'status': isAuthenticated ? 'success' : 'warning',
        'user_id': currentUser?.uid,
        'email': currentUser?.email,
        'message': isAuthenticated ? 'Utilisateur authentifié' : 'Pas d\'utilisateur connecté',
      };
      
      if (!isAuthenticated) {
        results['recommendations'].add('🔐 Se connecter avec un compte valide');
      }
      
    } catch (e) {
      results['tests']['auth'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Erreur authentification',
      };
    }
  }

  /// ⚙️ Test configuration Firestore
  static Future<void> _testFirestoreConfig(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] ⚙️ Test configuration Firestore...');
      
      // Vérifier les paramètres Firestore
      final settings = _firestore.settings;
      
      results['tests']['config'] = {
        'status': 'success',
        'host': settings.host,
        'ssl_enabled': settings.sslEnabled,
        'persistence_enabled': settings.persistenceEnabled,
        'message': 'Configuration Firestore OK',
      };
      
    } catch (e) {
      results['tests']['config'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Erreur configuration Firestore',
      };
      
      results['recommendations'].add('🔧 Vérifier la configuration Firebase');
    }
  }

  /// 🛡️ Test règles de sécurité
  static Future<void> _testSecurityRules(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🛡️ Test règles de sécurité...');
      
      // Essayer de lire une collection publique
      final testDoc = await _firestore
          .collection('test_public')
          .doc('test')
          .get(const GetOptions(source: Source.server));
      
      results['tests']['security'] = {
        'status': 'success',
        'can_read': true,
        'message': 'Règles de sécurité permettent la lecture',
      };
      
    } catch (e) {
      final isPermissionError = e.toString().contains('permission-denied');
      
      results['tests']['security'] = {
        'status': isPermissionError ? 'warning' : 'error',
        'can_read': false,
        'error': e.toString(),
        'message': isPermissionError 
            ? 'Règles de sécurité restrictives' 
            : 'Erreur accès Firestore',
      };
      
      if (isPermissionError) {
        results['recommendations'].add('🛡️ Vérifier les règles de sécurité Firestore');
      }
    }
  }

  /// 📝 Test opérations de base
  static Future<void> _testBasicOperations(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 📝 Test opérations de base...');
      
      final startTime = DateTime.now();
      
      // Test lecture
      try {
        await _firestore
            .collection('health_check')
            .doc('test')
            .get(const GetOptions(source: Source.server));
        
        final readTime = DateTime.now().difference(startTime).inMilliseconds;
        
        results['tests']['read'] = {
          'status': 'success',
          'latency_ms': readTime,
          'message': 'Lecture Firestore OK',
        };
        
      } catch (e) {
        results['tests']['read'] = {
          'status': 'failed',
          'error': e.toString(),
          'message': 'Échec lecture Firestore',
        };
      }
      
      // Test écriture
      try {
        final writeStartTime = DateTime.now();
        
        await _firestore
            .collection('health_check')
            .doc('test_write')
            .set({
          'test': true,
          'timestamp': FieldValue.serverTimestamp(),
          'diagnostic_id': DateTime.now().millisecondsSinceEpoch,
        });
        
        final writeTime = DateTime.now().difference(writeStartTime).inMilliseconds;
        
        results['tests']['write'] = {
          'status': 'success',
          'latency_ms': writeTime,
          'message': 'Écriture Firestore OK',
        };
        
      } catch (e) {
        results['tests']['write'] = {
          'status': 'failed',
          'error': e.toString(),
          'message': 'Échec écriture Firestore',
        };
      }
      
    } catch (e) {
      results['tests']['basic_ops'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Erreur tests opérations de base',
      };
    }
  }

  /// 🌐 Test latence réseau
  static Future<void> _testNetworkLatency(Map<String, dynamic> results) async {
    try {
      debugPrint('[FIRESTORE_DIAGNOSTIC] 🌐 Test latence réseau...');
      
      final latencies = <int>[];
      
      // Faire 3 tests de ping
      for (int i = 0; i < 3; i++) {
        final startTime = DateTime.now();
        
        try {
          await _firestore
              .collection('ping_test')
              .doc('ping')
              .get(const GetOptions(source: Source.server));
          
          final latency = DateTime.now().difference(startTime).inMilliseconds;
          latencies.add(latency);
          
        } catch (e) {
          // Ignorer les erreurs individuelles
        }
        
        // Attendre un peu entre les tests
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      if (latencies.isNotEmpty) {
        final avgLatency = latencies.reduce((a, b) => a + b) / latencies.length;
        
        results['tests']['latency'] = {
          'status': avgLatency < 1000 ? 'success' : 'warning',
          'average_ms': avgLatency.round(),
          'min_ms': latencies.reduce((a, b) => a < b ? a : b),
          'max_ms': latencies.reduce((a, b) => a > b ? a : b),
          'message': 'Latence réseau: ${avgLatency.round()}ms',
        };
        
        if (avgLatency > 2000) {
          results['recommendations'].add('🐌 Latence élevée - Vérifier la connexion');
        }
        
      } else {
        results['tests']['latency'] = {
          'status': 'failed',
          'message': 'Impossible de mesurer la latence',
        };
      }
      
    } catch (e) {
      results['tests']['latency'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Erreur test latence',
      };
    }
  }

  /// 📊 Analyser les résultats
  static void _analyzeResults(Map<String, dynamic> results) {
    final tests = results['tests'] as Map<String, dynamic>;
    final recommendations = results['recommendations'] as List<String>;
    
    int successCount = 0;
    int totalTests = 0;
    
    tests.forEach((testName, result) {
      totalTests++;
      if (result['status'] == 'success') {
        successCount++;
      }
    });
    
    // Déterminer le statut global
    if (successCount == totalTests) {
      results['overall_status'] = 'healthy';
      recommendations.add('✅ Firestore fonctionne parfaitement');
    } else if (successCount >= totalTests * 0.7) {
      results['overall_status'] = 'degraded';
      recommendations.add('⚠️ Firestore fonctionne partiellement');
    } else {
      results['overall_status'] = 'unhealthy';
      recommendations.add('❌ Problèmes majeurs avec Firestore');
    }
    
    // Recommandations spécifiques
    if (tests['internet']?['status'] != 'success') {
      recommendations.add('🔧 Redémarrer la connexion Internet');
    }
    
    if (tests['auth']?['status'] != 'success') {
      recommendations.add('🔧 Se reconnecter à Firebase Auth');
    }
    
    if (tests['read']?['status'] != 'success' || tests['write']?['status'] != 'success') {
      recommendations.add('🔧 Vérifier les règles Firestore et les permissions');
    }
    
    final avgLatency = tests['latency']?['average_ms'];
    if (avgLatency != null && avgLatency > 1000) {
      recommendations.add('🔧 Optimiser la connexion réseau');
    }
  }

  /// 🎯 Test rapide de disponibilité
  static Future<bool> isFirestoreAvailable() async {
    try {
      await _firestore
          .collection('health_check')
          .doc('quick_test')
          .get(const GetOptions(source: Source.server))
          .timeout(const Duration(seconds: 5));
      
      return true;
    } catch (e) {
      debugPrint('[FIRESTORE_DIAGNOSTIC] ❌ Firestore indisponible: $e');
      return false;
    }
  }

  /// 📋 Afficher un rapport formaté
  static String formatDiagnosticReport(Map<String, dynamic> diagnostic) {
    final buffer = StringBuffer();
    
    buffer.writeln('🔍 DIAGNOSTIC FIRESTORE');
    buffer.writeln('=' * 40);
    buffer.writeln('📅 ${diagnostic['timestamp']}');
    buffer.writeln('🎯 Statut: ${diagnostic['overall_status']}');
    buffer.writeln();
    
    buffer.writeln('📊 TESTS:');
    final tests = diagnostic['tests'] as Map<String, dynamic>;
    tests.forEach((testName, result) {
      final status = result['status'] == 'success' ? '✅' : 
                    result['status'] == 'warning' ? '⚠️' : '❌';
      buffer.writeln('  $status $testName: ${result['message']}');
    });
    
    buffer.writeln();
    buffer.writeln('💡 RECOMMANDATIONS:');
    final recommendations = diagnostic['recommendations'] as List<dynamic>;
    for (final rec in recommendations) {
      buffer.writeln('  • $rec');
    }
    
    return buffer.toString();
  }
}
