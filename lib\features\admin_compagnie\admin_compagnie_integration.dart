/// 🏢 INTÉGRATION COMPLÈTE DU SYSTÈME ADMIN COMPAGNIE
///
/// Ce fichier centralise tous les exports nécessaires pour utiliser
/// le système de sécurité et de gestion des Admin Compagnies

import 'package:flutter/material.dart';

// Services
export 'services/company_redirect_service.dart';
export 'services/company_security_middleware.dart';

// Screens
export 'presentation/screens/company_dashboard_screen.dart';

// Routes
export 'routes/company_routes.dart';

// Imports pour utilisation interne
import 'services/company_redirect_service.dart';
import 'services/company_security_middleware.dart';

/// 🎯 Guide d'intégration rapide
/// 
/// 1. AJOUTER LES ROUTES DANS VOTRE MAIN.dart :
/// ```dart
/// import 'package:constat_tunisie/features/admin_compagnie/admin_compagnie_integration.dart';
/// 
/// // Dans votre MaterialApp
/// onGenerateRoute: (settings) {
///   // Essayer les routes de compagnies d'abord
///   final companyRoute = CompanyRoutes.generateRoute(settings);
///   if (companyRoute != null) return companyRoute;
///   
///   // Puis vos autres routes...
///   return null;
/// }
/// ```
/// 
/// 2. MODIFIER VOTRE SERVICE D'AUTHENTIFICATION :
/// ```dart
/// // Après une connexion réussie
/// final redirectRoute = await CompanyRedirectService.getRedirectRouteForUser();
/// Navigator.pushReplacementNamed(context, redirectRoute);
/// ```
/// 
/// 3. DÉPLOYER LES RÈGLES FIRESTORE :
/// ```bash
/// firebase deploy --only firestore:rules
/// ```
/// 
/// 4. TESTER LA SÉCURITÉ :
/// ```dart
/// // Vérifier l'accès avant d'afficher des données
/// final hasAccess = await CompanySecurityMiddleware.canAccessCompanyData(compagnieId);
/// if (hasAccess) {
///   // Afficher les données
/// } else {
///   // Rediriger vers erreur
/// }
/// ```

/// 🚀 Classe utilitaire pour l'intégration rapide
class AdminCompagnieIntegration {
  
  /// 📱 Initialiser le système Admin Compagnie dans votre app
  static void initialize() {
    debugPrint('🏢 [ADMIN_COMPAGNIE] Système initialisé');
    debugPrint('🔐 [ADMIN_COMPAGNIE] Sécurité inter-compagnies activée');
    debugPrint('🛣️ [ADMIN_COMPAGNIE] Routes dynamiques configurées');
  }
  
  /// 🎯 Vérifier si le système est correctement configuré
  static Future<bool> verifyConfiguration() async {
    try {
      // Vérifier que les services sont accessibles
      final redirectService = CompanyRedirectService.getCurrentUserCompanyInfo();
      final securityService = CompanySecurityMiddleware.checkCompanyResourceAccess(
        compagnieId: 'test',
        resourceType: 'test',
      );
      
      await Future.wait([redirectService, securityService]);
      
      debugPrint('✅ [ADMIN_COMPAGNIE] Configuration vérifiée avec succès');
      return true;
      
    } catch (e) {
      debugPrint('❌ [ADMIN_COMPAGNIE] Erreur configuration: $e');
      return false;
    }
  }
  
  /// 📋 Obtenir les informations de diagnostic
  static Future<Map<String, dynamic>> getDiagnosticInfo() async {
    try {
      final userInfo = await CompanyRedirectService.getCurrentUserCompanyInfo();
      
      return {
        'isConfigured': true,
        'userConnected': userInfo != null,
        'userRole': userInfo?['userRole'],
        'compagnieId': userInfo?['id'],
        'compagnieNom': userInfo?['nom'],
        'timestamp': DateTime.now().toIso8601String(),
      };
      
    } catch (e) {
      return {
        'isConfigured': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}

/// 🎨 Widgets utilitaires pour l'interface Admin Compagnie
class AdminCompagnieWidgets {
  
  /// 🏢 Widget d'en-tête avec logo de compagnie
  static Widget buildCompanyHeader({
    required String compagnieNom,
    String? logoUrl,
    Color? couleur,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: couleur ?? Colors.blue,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          if (logoUrl != null)
            CircleAvatar(
              radius: 24,
              backgroundImage: NetworkImage(logoUrl),
            ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  compagnieNom,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Text(
                  'Dashboard Administrateur',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// 🚫 Widget d'erreur d'accès
  static Widget buildAccessDeniedWidget({
    required String message,
    VoidCallback? onRetry,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.security,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 24),
            const Text(
              '🚫 Accès Refusé',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onRetry,
                child: const Text('Réessayer'),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// ⏳ Widget de chargement avec validation de sécurité
  static Widget buildSecurityValidationWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('🔐 Validation des accès...'),
          SizedBox(height: 8),
          Text(
            'Vérification des permissions en cours',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

/// 🔧 Extensions utilitaires pour BuildContext
extension AdminCompagnieExtensions on BuildContext {
  
  /// 🏢 Naviguer vers le dashboard de compagnie avec validation
  Future<void> navigateToCompanyDashboardSafe(String compagnieId) async {
    final hasAccess = await CompanySecurityMiddleware.canAccessFirestoreCollection(
      compagnieId: compagnieId,
      collection: 'company_dashboard',
    );
    
    if (hasAccess) {
      Navigator.pushNamed(this, '/compagnie-dashboard/$compagnieId');
    } else {
      ScaffoldMessenger.of(this).showSnackBar(
        const SnackBar(
          content: Text('🚫 Accès refusé à cette compagnie'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// 🔄 Redirection automatique intelligente
  Future<void> smartRedirect() async {
    final route = await CompanyRedirectService.getRedirectRouteForUser();
    Navigator.pushReplacementNamed(this, route);
  }
}

/// 📊 Constantes pour le système Admin Compagnie
class AdminCompagnieConstants {
  
  // Routes
  static const String companyDashboardRoute = '/compagnie-dashboard';
  static const String companyAgentsRoute = '/compagnie-dashboard/{id}/agents';
  static const String companyAgenciesRoute = '/compagnie-dashboard/{id}/agences';
  
  // Rôles
  static const String roleAdminCompagnie = 'admin_compagnie';
  static const String roleSuperAdmin = 'super_admin';
  
  // Collections Firestore
  static const String usersCollection = 'users';
  static const String compagniesCollection = 'compagnies_assurance';
  static const String agencesCollection = 'agences_assurance';
  static const String securityLogsCollection = 'security_violations';
  
  // Messages d'erreur
  static const String accessDeniedMessage = 'Accès refusé: Vous ne pouvez accéder qu\'aux données de votre compagnie';
  static const String userNotFoundMessage = 'Compte utilisateur non trouvé';
  static const String companyNotFoundMessage = 'Compagnie associée non trouvée';
}
