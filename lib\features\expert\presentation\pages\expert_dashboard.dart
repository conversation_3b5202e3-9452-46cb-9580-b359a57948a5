import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';

/// 🔧 Dashboard Expert Automobile
/// 
/// Interface spécifique pour les experts automobiles
/// Permet de gérer les expertises, rapports et sinistres assignés
class ExpertDashboard extends StatefulWidget {
  const ExpertDashboard({Key? key}) : super(key: key);

  @override
  State<ExpertDashboard> createState() => _ExpertDashboardState();
}

class _ExpertDashboardState extends State<ExpertDashboard> {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  Map<String, dynamic>? _userData;
  List<Map<String, dynamic>> _compagniesPartenaires = [];
  bool _isLoading = true;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 📊 Charger les données de l'expert
  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Charger les données de l'utilisateur
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        final userDoc = await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .get();

        if (userDoc.exists) {
          _userData = userDoc.data();
        }

        // Charger les compagnies partenaires
        await _loadCompagniesPartenaires();
      }

    } catch (e) {
      debugPrint('[EXPERT_DASHBOARD] ❌ Erreur chargement données: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🏢 Charger les compagnies partenaires
  Future<void> _loadCompagniesPartenaires() async {
    try {
      // Les experts peuvent travailler avec plusieurs compagnies
      final compagniesSnapshot = await _firestore
          .collection('compagnies_assurance')
          .where('expertsPartenaires', arrayContains: _auth.currentUser?.uid)
          .get();

      _compagniesPartenaires = compagniesSnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();

    } catch (e) {
      debugPrint('[EXPERT_DASHBOARD] ❌ Erreur chargement compagnies: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildAppBar(),
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  /// 📱 Construire l'AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          Icon(Icons.engineering, color: ModernTheme.primaryColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Expert Automobile',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${_compagniesPartenaires.length} compagnies partenaires',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      iconTheme: IconThemeData(color: ModernTheme.primaryColor),
      actions: [
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () {
            // TODO: Afficher les notifications
          },
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'profile':
                _showProfile();
                break;
              case 'settings':
                _showSettings();
                break;
              case 'logout':
                _logout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Row(
                children: [
                  Icon(Icons.person),
                  SizedBox(width: 8),
                  Text('Profil'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings),
                  SizedBox(width: 8),
                  Text('Paramètres'),
                ],
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: Row(
                children: [
                  Icon(Icons.logout, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Déconnexion', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 🗂️ Construire le Drawer
  Widget _buildDrawer() {
    return Drawer(
      child: Column(
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [ModernTheme.primaryColor, ModernTheme.accentColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Text(
                    (_userData?['prenom']?[0] ?? 'E').toUpperCase(),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: ModernTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_userData?['prenom'] ?? ''} ${_userData?['nom'] ?? ''}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Expert Automobile',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.dashboard,
                  title: 'Tableau de bord',
                  index: 0,
                ),
                _buildDrawerItem(
                  icon: Icons.assignment,
                  title: 'Expertises assignées',
                  index: 1,
                ),
                _buildDrawerItem(
                  icon: Icons.description,
                  title: 'Rapports d\'expertise',
                  index: 2,
                ),
                _buildDrawerItem(
                  icon: Icons.business,
                  title: 'Compagnies partenaires',
                  index: 3,
                ),
                _buildDrawerItem(
                  icon: Icons.calendar_today,
                  title: 'Planning',
                  index: 4,
                ),
                _buildDrawerItem(
                  icon: Icons.analytics,
                  title: 'Statistiques',
                  index: 5,
                ),
                const Divider(),
                _buildDrawerItem(
                  icon: Icons.settings,
                  title: 'Paramètres',
                  index: 6,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📋 Construire un élément du drawer
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? ModernTheme.primaryColor : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? ModernTheme.primaryColor : Colors.grey[800],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      selectedTileColor: ModernTheme.primaryColor.withOpacity(0.1),
      onTap: () {
        setState(() => _selectedIndex = index);
        Navigator.of(context).pop();
      },
    );
  }

  /// 📱 Construire le corps de la page
  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardView();
      case 1:
        return _buildExpertisesView();
      case 2:
        return _buildRapportsView();
      case 3:
        return _buildCompagniesView();
      case 4:
        return _buildPlanningView();
      case 5:
        return _buildStatistiquesView();
      case 6:
        return _buildParametresView();
      default:
        return _buildDashboardView();
    }
  }

  /// 📊 Vue tableau de bord
  Widget _buildDashboardView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tableau de bord Expert',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildStatCard(
                  title: 'Expertises en cours',
                  value: '8',
                  icon: Icons.assignment,
                  color: Colors.blue,
                ),
                _buildStatCard(
                  title: 'Rapports terminés',
                  value: '156',
                  icon: Icons.description,
                  color: Colors.green,
                ),
                _buildStatCard(
                  title: 'Compagnies',
                  value: '${_compagniesPartenaires.length}',
                  icon: Icons.business,
                  color: Colors.orange,
                ),
                _buildStatCard(
                  title: 'Délai moyen',
                  value: '2.5j',
                  icon: Icons.timer,
                  color: Colors.purple,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 📊 Construire une carte de statistique
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 48, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 📋 Vue expertises assignées
  Widget _buildExpertisesView() {
    return const Center(
      child: Text('Expertises assignées - En développement'),
    );
  }

  /// 📄 Vue rapports
  Widget _buildRapportsView() {
    return const Center(
      child: Text('Rapports d\'expertise - En développement'),
    );
  }

  /// 🏢 Vue compagnies partenaires
  Widget _buildCompagniesView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Compagnies partenaires',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: _compagniesPartenaires.length,
              itemBuilder: (context, index) {
                final compagnie = _compagniesPartenaires[index];
                return Card(
                  child: ListTile(
                    leading: CircleAvatar(
                      backgroundColor: ModernTheme.primaryColor,
                      child: Text(
                        compagnie['nom']?[0] ?? 'C',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(compagnie['nom'] ?? 'Compagnie'),
                    subtitle: Text(compagnie['email'] ?? ''),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () {
                      // TODO: Afficher les détails de la compagnie
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 📅 Vue planning
  Widget _buildPlanningView() {
    return const Center(
      child: Text('Planning - En développement'),
    );
  }

  /// 📈 Vue statistiques
  Widget _buildStatistiquesView() {
    return const Center(
      child: Text('Statistiques - En développement'),
    );
  }

  /// ⚙️ Vue paramètres
  Widget _buildParametresView() {
    return const Center(
      child: Text('Paramètres - En développement'),
    );
  }

  /// 👤 Afficher le profil
  void _showProfile() {
    // TODO: Implémenter l'affichage du profil
  }

  /// ⚙️ Afficher les paramètres
  void _showSettings() {
    // TODO: Implémenter les paramètres
  }

  /// 🚪 Déconnexion
  void _logout() async {
    await _auth.signOut();
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }
}
