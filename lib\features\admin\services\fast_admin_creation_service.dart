import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/services/offline_admin_service.dart';
import 'simple_email_service.dart';

/// ⚡ Service de création d'admin ULTRA-RAPIDE
/// 
/// Va directement au mode hors ligne si Firestore ne répond pas rapidement
class FastAdminCreationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// ⚡ Créer un Admin Compagnie avec timeout ultra-court
  static Future<Map<String, dynamic>> createAdminCompagnieUltraFast({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieName,
  }) async {
    debugPrint('[FAST_ADMIN] ⚡ CRÉATION ULTRA-RAPIDE Admin Compagnie...');
    debugPrint('[FAST_ADMIN] 📧 Email: $email');
    debugPrint('[FAST_ADMIN] 🏢 Compagnie: $compagnieName ($compagnieId)');

    // 🚀 ÉTAPE 1: Test Firestore ultra-rapide (2 secondes max)
    final isFirestoreAvailable = await _testFirestoreUltraFast();
    
    if (isFirestoreAvailable) {
      debugPrint('[FAST_ADMIN] ✅ Firestore disponible - Tentative création normale...');
      
      try {
        return await _createViaFirestore(
          email: email,
          nom: nom,
          prenom: prenom,
          compagnieId: compagnieId,
          compagnieName: compagnieName,
        );
      } catch (e) {
        debugPrint('[FAST_ADMIN] ❌ Création Firestore échouée: $e');
        // Fallback immédiat
      }
    } else {
      debugPrint('[FAST_ADMIN] ⚠️ Firestore indisponible - Mode hors ligne immédiat');
    }

    // 🚀 ÉTAPE 2: Mode hors ligne immédiat
    return await _createOfflineImmediate(
      email: email,
      nom: nom,
      prenom: prenom,
      compagnieId: compagnieId,
      compagnieName: compagnieName,
    );
  }

  /// ⚡ Test Firestore ultra-rapide (2 secondes max)
  static Future<bool> _testFirestoreUltraFast() async {
    try {
      debugPrint('[FAST_ADMIN] ⚡ Test Firestore ultra-rapide...');
      
      await _firestore
          .collection('health_check')
          .doc('ping')
          .get(const GetOptions(source: Source.server))
          .timeout(const Duration(seconds: 2));
      
      debugPrint('[FAST_ADMIN] ✅ Firestore répond rapidement');
      return true;
      
    } catch (e) {
      debugPrint('[FAST_ADMIN] ❌ Firestore ne répond pas rapidement: $e');
      return false;
    }
  }

  /// 🔥 Création via Firestore (avec timeout)
  static Future<Map<String, dynamic>> _createViaFirestore({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieName,
  }) async {
    debugPrint('[FAST_ADMIN] 🔥 Création via Firestore...');

    // Générer mot de passe sécurisé
    final temporaryPassword = _generateSecurePassword();

    // Créer le compte Firebase Auth
    final userCredential = await _auth.createUserWithEmailAndPassword(
      email: email,
      password: temporaryPassword,
    );

    final userId = userCredential.user!.uid;

    // Créer le document utilisateur dans Firestore
    await _firestore
        .collection('users')
        .doc(userId)
        .set({
      'email': email,
      'nom': nom,
      'prenom': prenom,
      'role': 'admin_compagnie',
      'compagnieId': compagnieId,
      'compagnieName': compagnieName,
      'isFirstLogin': true,
      'isActive': true,
      'createdAt': FieldValue.serverTimestamp(),
      'createdBy': 'super_admin',
      'source': 'direct_creation',
    }).timeout(const Duration(seconds: 5));

    debugPrint('[FAST_ADMIN] ✅ Compte créé dans Firestore');

    // Essayer d'envoyer l'email (sans bloquer)
    _sendEmailAsync(
      email: email,
      nom: nom,
      temporaryPassword: temporaryPassword,
      compagnieName: compagnieName,
    );

    return {
      'success': true,
      'userId': userId,
      'email': email,
      'temporaryPassword': temporaryPassword,
      'method': 'firestore',
      'message': 'Admin Compagnie créé avec succès via Firestore',
    };
  }

  /// 📱 Création hors ligne immédiate
  static Future<Map<String, dynamic>> _createOfflineImmediate({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieName,
  }) async {
    debugPrint('[FAST_ADMIN] 📱 Création hors ligne immédiate...');

    final result = await OfflineAdminService.createAdminCompagnieOffline(
      email: email,
      nom: nom,
      prenom: prenom,
      compagnieId: compagnieId,
      compagnieNom: compagnieName,
    );

    if (result['success']) {
      debugPrint('[FAST_ADMIN] ✅ Admin créé hors ligne avec succès');
      
      // Essayer d'envoyer l'email (sans bloquer)
      _sendEmailAsync(
        email: email,
        nom: nom,
        temporaryPassword: result['temporaryPassword'],
        compagnieName: compagnieName,
      );
    }

    return result;
  }

  /// 📧 Envoyer email en arrière-plan (sans bloquer)
  static void _sendEmailAsync({
    required String email,
    required String nom,
    required String temporaryPassword,
    required String compagnieName,
  }) {
    // Lancer l'envoi d'email en arrière-plan
    Future.delayed(const Duration(milliseconds: 100), () async {
      try {
        debugPrint('[FAST_ADMIN] 📧 Tentative envoi email en arrière-plan...');
        
        final emailSent = await SimpleEmailService.sendAccountCreatedEmail(
          userEmail: email,
          userName: nom,
          temporaryPassword: temporaryPassword,
          role: 'admin_compagnie',
          companyName: compagnieName,
        );

        if (emailSent) {
          debugPrint('[FAST_ADMIN] ✅ Email envoyé avec succès');
        } else {
          debugPrint('[FAST_ADMIN] ⚠️ Email non envoyé (pas critique)');
        }
        
      } catch (e) {
        debugPrint('[FAST_ADMIN] ❌ Erreur envoi email (pas critique): $e');
      }
    });
  }

  /// 🔐 Générer un mot de passe sécurisé
  static String _generateSecurePassword() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = timestamp % 10000;
    return 'Admin$random!';
  }

  /// 🧪 Test de disponibilité ultra-rapide
  static Future<bool> isFirestoreAvailableUltraFast() async {
    return await _testFirestoreUltraFast();
  }

  /// 📊 Statistiques de création
  static Future<Map<String, dynamic>> getCreationStats() async {
    try {
      return {
        'firestore_available': await _testFirestoreUltraFast(),
        'last_check': DateTime.now().toIso8601String(),
        'service_status': 'operational',
      };

    } catch (e) {
      debugPrint('[FAST_ADMIN] ❌ Erreur stats: $e');
      return {
        'error': e.toString(),
        'firestore_available': false,
        'service_status': 'error',
      };
    }
  }
}
