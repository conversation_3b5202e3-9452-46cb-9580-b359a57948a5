import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🧭 Service de navigation par rôle
/// 
/// Redirige automatiquement les utilisateurs vers leur interface spécifique
/// selon leur rôle après connexion
class RoleNavigationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🚀 Rediriger l'utilisateur vers son interface appropriée
  static Future<void> navigateToRoleInterface(BuildContext context) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('[ROLE_NAV] ❌ Aucun utilisateur connecté');
        _navigateToLogin(context);
        return;
      }

      debugPrint('[ROLE_NAV] 🔍 Détermination interface pour: ${currentUser.email}');

      // Récupérer les données utilisateur
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        debugPrint('[ROLE_NAV] ❌ Document utilisateur non trouvé');
        _showUserNotFoundDialog(context);
        return;
      }

      final userData = userDoc.data()!;
      final role = userData['role'] as String?;
      final compagnieId = userData['compagnieId'] as String?;
      final agenceId = userData['agenceId'] as String?;

      debugPrint('[ROLE_NAV] 👤 Rôle détecté: $role');
      debugPrint('[ROLE_NAV] 🏢 Compagnie: $compagnieId');
      debugPrint('[ROLE_NAV] 🏪 Agence: $agenceId');

      // Vérifier si c'est la première connexion
      final isFirstLogin = userData['passwordChangeRequired'] == true;
      if (isFirstLogin) {
        debugPrint('[ROLE_NAV] 🔑 Première connexion - Changement mot de passe requis');
        _navigateToPasswordChange(context, role);
        return;
      }

      // Rediriger selon le rôle
      switch (role) {
        case 'super_admin':
          debugPrint('[ROLE_NAV] 🚀 Redirection Super Admin Dashboard');
          Navigator.of(context).pushReplacementNamed('/super-admin-dashboard');
          break;

        case 'admin_compagnie':
          if (compagnieId != null) {
            debugPrint('[ROLE_NAV] 🏢 Redirection Admin Compagnie Dashboard');
            Navigator.of(context).pushReplacementNamed('/compagnie-dashboard/$compagnieId');
          } else {
            _showConfigurationError(context, 'Compagnie non assignée');
          }
          break;

        case 'admin_agence':
          if (compagnieId != null && agenceId != null) {
            debugPrint('[ROLE_NAV] 🏪 Redirection Admin Agence Dashboard');
            Navigator.of(context).pushReplacementNamed('/agence-dashboard/$agenceId');
          } else {
            _showConfigurationError(context, 'Agence non assignée');
          }
          break;

        case 'agent_agence':
          if (agenceId != null) {
            debugPrint('[ROLE_NAV] 👨‍💼 Redirection Agent Interface');
            Navigator.of(context).pushReplacementNamed('/agent-dashboard/$agenceId');
          } else {
            _showConfigurationError(context, 'Agence non assignée');
          }
          break;

        case 'expert_auto':
          debugPrint('[ROLE_NAV] 🔧 Redirection Expert Interface');
          Navigator.of(context).pushReplacementNamed('/expert-dashboard');
          break;

        case 'conducteur':
          debugPrint('[ROLE_NAV] 🚗 Redirection Conducteur Interface');
          Navigator.of(context).pushReplacementNamed('/conducteur-dashboard');
          break;

        default:
          debugPrint('[ROLE_NAV] ❌ Rôle non reconnu: $role');
          _showUnknownRoleDialog(context, role);
          break;
      }

    } catch (e) {
      debugPrint('[ROLE_NAV] ❌ Erreur navigation: $e');
      _showNavigationError(context, e.toString());
    }
  }

  /// 🔑 Rediriger vers le changement de mot de passe
  static void _navigateToPasswordChange(BuildContext context, String? role) {
    Navigator.of(context).pushReplacementNamed(
      '/change-password',
      arguments: {'role': role},
    );
  }

  /// 🔐 Rediriger vers la page de connexion
  static void _navigateToLogin(BuildContext context) {
    Navigator.of(context).pushReplacementNamed('/login');
  }

  /// ❌ Afficher dialog utilisateur non trouvé
  static void _showUserNotFoundDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Compte non configuré'),
          ],
        ),
        content: const Text(
          'Votre compte n\'est pas correctement configuré dans le système. '
          'Veuillez contacter l\'administrateur.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin(context);
            },
            child: const Text('Retour à la connexion'),
          ),
        ],
      ),
    );
  }

  /// ⚙️ Afficher dialog erreur de configuration
  static void _showConfigurationError(BuildContext context, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.orange),
            SizedBox(width: 8),
            Text('Configuration incomplète'),
          ],
        ),
        content: Text(
          'Erreur de configuration: $error\n\n'
          'Veuillez contacter l\'administrateur pour compléter la configuration de votre compte.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin(context);
            },
            child: const Text('Retour à la connexion'),
          ),
        ],
      ),
    );
  }

  /// ❓ Afficher dialog rôle inconnu
  static void _showUnknownRoleDialog(BuildContext context, String? role) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help, color: Colors.blue),
            SizedBox(width: 8),
            Text('Rôle non reconnu'),
          ],
        ),
        content: Text(
          'Rôle non reconnu: ${role ?? "null"}\n\n'
          'Veuillez contacter l\'administrateur pour corriger votre rôle.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin(context);
            },
            child: const Text('Retour à la connexion'),
          ),
        ],
      ),
    );
  }

  /// 💥 Afficher dialog erreur de navigation
  static void _showNavigationError(BuildContext context, String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Erreur de navigation'),
          ],
        ),
        content: Text(
          'Une erreur s\'est produite lors de la redirection:\n\n$error\n\n'
          'Veuillez réessayer ou contacter le support.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin(context);
            },
            child: const Text('Retour à la connexion'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              navigateToRoleInterface(context); // Réessayer
            },
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

  /// 📊 Obtenir les informations de rôle pour l'affichage
  static Future<Map<String, dynamic>?> getUserRoleInfo() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return null;

      final userData = userDoc.data()!;
      return {
        'role': userData['role'],
        'compagnieId': userData['compagnieId'],
        'agenceId': userData['agenceId'],
        'nom': userData['nom'],
        'prenom': userData['prenom'],
        'email': userData['email'],
      };
    } catch (e) {
      debugPrint('[ROLE_NAV] ❌ Erreur récupération info rôle: $e');
      return null;
    }
  }

  /// 🎯 Vérifier si l'utilisateur a accès à une route spécifique
  static Future<bool> canAccessRoute(String route) async {
    try {
      final roleInfo = await getUserRoleInfo();
      if (roleInfo == null) return false;

      final role = roleInfo['role'] as String?;
      
      // Définir les routes autorisées par rôle
      final roleRoutes = {
        'super_admin': ['/super-admin-dashboard', '/admin-dashboard'],
        'admin_compagnie': ['/compagnie-dashboard'],
        'admin_agence': ['/agence-dashboard'],
        'agent_agence': ['/agent-dashboard'],
        'expert_auto': ['/expert-dashboard'],
        'conducteur': ['/conducteur-dashboard'],
      };

      final allowedRoutes = roleRoutes[role] ?? [];
      return allowedRoutes.any((allowedRoute) => route.startsWith(allowedRoute));

    } catch (e) {
      debugPrint('[ROLE_NAV] ❌ Erreur vérification accès route: $e');
      return false;
    }
  }
}
