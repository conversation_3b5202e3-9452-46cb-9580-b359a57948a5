import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// 🔐 Service d'authentification robuste qui contourne les erreurs Firebase Auth
class RobustAuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔐 Connexion robuste avec gestion d'erreurs
  static Future<Map<String, dynamic>> robustLogin({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('[ROBUST_AUTH] 🚀 Début connexion robuste: $email');

      // 1. Tentative de connexion Firebase Auth
      UserCredential? userCredential;
      User? user;
      
      try {
        userCredential = await _auth.signInWithEmailAndPassword(
          email: email,
          password: password,
        );
        user = userCredential.user;
        debugPrint('[ROBUST_AUTH] ✅ Firebase Auth réussi: ${user?.uid}');
      } catch (authError) {
        debugPrint('[ROBUST_AUTH] ⚠️ Erreur Firebase Auth: $authError');
        
        // Si l'erreur est de type cast (problème connu), essayer de récupérer l'utilisateur
        if (authError.toString().contains('is not a subtype of type')) {
          debugPrint('[ROBUST_AUTH] 🔧 Tentative de récupération après erreur de type...');
          
          // Attendre un peu et essayer de récupérer l'utilisateur
          await Future.delayed(const Duration(milliseconds: 1000));
          user = _auth.currentUser;
          
          if (user != null) {
            debugPrint('[ROBUST_AUTH] ✅ Utilisateur récupéré après erreur: ${user.uid}');
          }
        }
        
        if (user == null) {
          return {
            'success': false,
            'error': 'Erreur de connexion Firebase Auth',
            'details': authError.toString(),
          };
        }
      }

      // 2. Vérifier le document Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(user!.uid)
          .get();

      if (!userDoc.exists) {
        debugPrint('[ROBUST_AUTH] ❌ Document Firestore non trouvé pour: ${user.uid}');
        return {
          'success': false,
          'error': 'Compte non configuré',
          'details': 'Document utilisateur manquant dans Firestore',
          'uid': user.uid,
          'email': user.email,
        };
      }

      final userData = userDoc.data()!;
      debugPrint('[ROBUST_AUTH] ✅ Document Firestore trouvé: ${userData['role']}');

      // 3. Vérifier le statut du compte
      if (userData['status'] != 'actif') {
        debugPrint('[ROBUST_AUTH] ❌ Compte inactif: ${userData['status']}');
        return {
          'success': false,
          'error': 'Compte inactif',
          'details': 'Statut: ${userData['status']}',
        };
      }

      // 4. Succès complet
      return {
        'success': true,
        'user': user,
        'userData': userData,
        'uid': user.uid,
        'email': user.email,
        'role': userData['role'],
        'status': userData['status'],
      };

    } catch (e) {
      debugPrint('[ROBUST_AUTH] ❌ Erreur générale: $e');
      return {
        'success': false,
        'error': 'Erreur de connexion',
        'details': e.toString(),
      };
    }
  }

  /// 🔍 Diagnostic complet d'un compte
  static Future<Map<String, dynamic>> diagnosticAccount(String email) async {
    try {
      debugPrint('[ROBUST_AUTH] 🔍 Diagnostic pour: $email');

      // 1. Vérifier si l'utilisateur existe dans Firebase Auth
      User? currentUser = _auth.currentUser;
      bool authExists = currentUser != null && currentUser.email == email;

      // 2. Chercher dans Firestore par email
      final querySnapshot = await _firestore
          .collection('users')
          .where('email', isEqualTo: email)
          .limit(1)
          .get();

      bool firestoreExists = querySnapshot.docs.isNotEmpty;
      Map<String, dynamic>? firestoreData;
      String? firestoreUid;

      if (firestoreExists) {
        firestoreData = querySnapshot.docs.first.data();
        firestoreUid = querySnapshot.docs.first.id;
      }

      return {
        'email': email,
        'firebase_auth_exists': authExists,
        'firebase_auth_uid': currentUser?.uid,
        'firestore_exists': firestoreExists,
        'firestore_uid': firestoreUid,
        'firestore_data': firestoreData,
        'role': firestoreData?['role'],
        'status': firestoreData?['status'],
        'compagnieId': firestoreData?['compagnieId'],
      };

    } catch (e) {
      debugPrint('[ROBUST_AUTH] ❌ Erreur diagnostic: $e');
      return {
        'error': e.toString(),
      };
    }
  }

  /// 🔧 Réparation automatique d'un compte
  static Future<bool> repairAccount(String email) async {
    try {
      debugPrint('[ROBUST_AUTH] 🔧 Réparation automatique pour: $email');

      final diagnostic = await diagnosticAccount(email);
      
      // Si le compte Firestore n'existe pas mais Firebase Auth oui
      if (diagnostic['firebase_auth_exists'] == true && 
          diagnostic['firestore_exists'] == false) {
        
        debugPrint('[ROBUST_AUTH] 🔧 Création document Firestore manquant...');
        
        // Créer le document Firestore basé sur l'email
        Map<String, dynamic> userData;
        
        if (email == '<EMAIL>') {
          // Super Admin
          userData = {
            'email': email,
            'nom': 'Super',
            'prenom': 'Admin',
            'role': 'super_admin',
            'status': 'actif',
            'created_at': FieldValue.serverTimestamp(),
            'updated_at': FieldValue.serverTimestamp(),
            'created_by': 'robust_auth_repair',
          };
        } else if (email == '<EMAIL>') {
          // Admin Compagnie
          userData = {
            'email': email,
            'nom': 'Admin',
            'prenom': 'Compagnie',
            'role': 'admin_compagnie',
            'status': 'actif',
            'compagnieId': 'star-assurance-test',
            'compagnieNom': 'STAR Assurance',
            'created_at': FieldValue.serverTimestamp(),
            'updated_at': FieldValue.serverTimestamp(),
            'created_by': 'robust_auth_repair',
          };
        } else {
          debugPrint('[ROBUST_AUTH] ❌ Email non reconnu pour réparation: $email');
          return false;
        }

        await _firestore
            .collection('users')
            .doc(diagnostic['firebase_auth_uid'])
            .set(userData);

        debugPrint('[ROBUST_AUTH] ✅ Document Firestore créé avec succès');
        return true;
      }

      debugPrint('[ROBUST_AUTH] ⚠️ Aucune réparation nécessaire');
      return true;

    } catch (e) {
      debugPrint('[ROBUST_AUTH] ❌ Erreur réparation: $e');
      return false;
    }
  }

  /// 🚀 Connexion avec réparation automatique
  static Future<Map<String, dynamic>> loginWithAutoRepair({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('[ROBUST_AUTH] 🚀 Connexion avec réparation automatique: $email');

      // 1. Tentative de connexion normale
      var result = await robustLogin(email: email, password: password);
      
      if (result['success'] == true) {
        debugPrint('[ROBUST_AUTH] ✅ Connexion réussie du premier coup');
        return result;
      }

      // 2. Si échec, diagnostic et réparation
      debugPrint('[ROBUST_AUTH] 🔧 Échec connexion, tentative de réparation...');
      
      final repairSuccess = await repairAccount(email);
      
      if (!repairSuccess) {
        debugPrint('[ROBUST_AUTH] ❌ Réparation échouée');
        return result; // Retourner l'erreur originale
      }

      // 3. Nouvelle tentative après réparation
      debugPrint('[ROBUST_AUTH] 🔄 Nouvelle tentative après réparation...');
      await Future.delayed(const Duration(milliseconds: 1000));
      
      result = await robustLogin(email: email, password: password);
      
      if (result['success'] == true) {
        debugPrint('[ROBUST_AUTH] ✅ Connexion réussie après réparation !');
      }

      return result;

    } catch (e) {
      debugPrint('[ROBUST_AUTH] ❌ Erreur connexion avec réparation: $e');
      return {
        'success': false,
        'error': 'Erreur système',
        'details': e.toString(),
      };
    }
  }

  /// 🔍 Obtenir les informations de l'utilisateur connecté
  static Future<Map<String, dynamic>?> getCurrentUserInfo() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (!userDoc.exists) return null;

      return {
        'uid': user.uid,
        'email': user.email,
        'userData': userDoc.data(),
      };

    } catch (e) {
      debugPrint('[ROBUST_AUTH] ❌ Erreur getCurrentUserInfo: $e');
      return null;
    }
  }
}
