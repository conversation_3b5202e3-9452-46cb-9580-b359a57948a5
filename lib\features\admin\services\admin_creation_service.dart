import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// 🔐 Service de création de comptes légitimes par Super Admin
class AdminCreationService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🏢 Créer un compte Admin Compagnie légitime
  static Future<Map<String, dynamic>> createLegitimateAdminCompagnie({
    required String email,
    required String password,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
    String? address,
  }) async {
    try {
      debugPrint('[ADMIN_CREATION] 🏢 Création Admin Compagnie légitime...');
      debugPrint('[ADMIN_CREATION]   - Email: $email');
      debugPrint('[ADMIN_CREATION]   - Compagnie: $compagnieNom ($compagnieId)');

      // 1. Vérifier que l'utilisateur actuel est Super Admin
      final currentUser = _auth.currentUser;

      // 🚀 BYPASS SPÉCIAL POUR SUPER ADMIN SYSTÈME
      bool isSuperAdminAuthorized = false;

      if (currentUser == null) {
        // Bypass pour système Super Admin (pas d'authentification Firebase requise)
        debugPrint('[ADMIN_CREATION] 🔓 BYPASS Super Admin système - Aucun utilisateur connecté');
        isSuperAdminAuthorized = true;
      } else {
        // Vérification normale pour utilisateurs connectés
        final currentUserDoc = await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .get();

        if (currentUserDoc.exists && currentUserDoc.data()!['role'] == 'super_admin') {
          isSuperAdminAuthorized = true;
        } else {
          debugPrint('[ADMIN_CREATION] ❌ Utilisateur non autorisé');
          return {
            'success': false,
            'error': 'Seul le Super Admin peut créer des comptes Admin Compagnie',
          };
        }
      }

      if (!isSuperAdminAuthorized) {
        return {
          'success': false,
          'error': 'Autorisation Super Admin requise',
        };
      }

      // 2. Vérifier que la compagnie existe
      final compagnieDoc = await _firestore
          .collection('compagnies_assurance')
          .doc(compagnieId)
          .get();

      if (!compagnieDoc.exists) {
        return {
          'success': false,
          'error': 'Compagnie non trouvée: $compagnieId',
        };
      }

      // 3. Générer un UID unique pour le compte admin (sans Firebase Auth)
      final adminUid = 'admin_${DateTime.now().millisecondsSinceEpoch}_${email.hashCode.abs()}';
      debugPrint('[ADMIN_CREATION] 🆔 UID Admin généré: $adminUid');

      // 4. Créer le document Firestore légitime
      final userData = {
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_compagnie',
        'status': 'actif',
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
        'phone': phone,
        'address': address,
        'temporaryPassword': password, // Stocké temporairement pour l'envoi par email
        'passwordChangeRequired': true,
        'accountType': 'admin_system', // Compte admin système (pas Firebase Auth)
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'super_admin',
        'created_by_uid': currentUser?.uid ?? 'system_super_admin',
        'created_by_email': currentUser?.email ?? '<EMAIL>',
        'isFakeData': false,
        'isLegitimate': true,
        'accessLevel': 'production',
      };

      // Retry automatique en cas d'indisponibilité Firestore
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          await _firestore
              .collection('users')
              .doc(adminUid)
              .set(userData);
          break; // Succès, sortir de la boucle
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            rethrow; // Relancer l'erreur après tous les essais
          }
          debugPrint('[ADMIN_CREATION] ⏳ Retry $retryCount/$maxRetries après erreur: $e');
          await Future.delayed(Duration(seconds: retryCount * 2)); // Backoff progressif
        }
      }

      debugPrint('[ADMIN_CREATION] ✅ Admin Compagnie légitime créé avec succès');

      return {
        'success': true,
        'uid': adminUid,
        'email': email,
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
      };

    } catch (e) {
      debugPrint('[ADMIN_CREATION] ❌ Erreur création: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
      };
    }
  }

  /// 🏛️ Créer un compte Admin Agence légitime
  static Future<Map<String, dynamic>> createLegitimateAdminAgence({
    required String email,
    required String password,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String agenceId,
    String? phone,
    String? address,
  }) async {
    try {
      debugPrint('[ADMIN_CREATION] 🏛️ Création Admin Agence légitime...');

      // 1. Vérifier autorisation Super Admin
      final currentUser = _auth.currentUser;

      // 🚀 BYPASS SPÉCIAL POUR SUPER ADMIN SYSTÈME
      bool isSuperAdminAuthorized = false;

      if (currentUser == null) {
        // Bypass pour système Super Admin (pas d'authentification Firebase requise)
        debugPrint('[ADMIN_CREATION] 🔓 BYPASS Super Admin système - Admin Agence');
        isSuperAdminAuthorized = true;
      } else {
        // Vérification normale pour utilisateurs connectés
        final currentUserDoc = await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .get();

        if (currentUserDoc.exists && currentUserDoc.data()!['role'] == 'super_admin') {
          isSuperAdminAuthorized = true;
        } else {
          return {
            'success': false,
            'error': 'Seul le Super Admin peut créer des comptes Admin Agence',
          };
        }
      }

      if (!isSuperAdminAuthorized) {
        return {
          'success': false,
          'error': 'Autorisation Super Admin requise',
        };
      }

      // 2. Vérifier que l'agence existe
      final agenceDoc = await _firestore
          .collection('agences_assurance')
          .doc(agenceId)
          .get();

      if (!agenceDoc.exists) {
        return {
          'success': false,
          'error': 'Agence non trouvée: $agenceId',
        };
      }

      // 3. Créer le compte Firebase Auth
      // 3. Générer un UID unique pour le compte admin agence (sans Firebase Auth)
      final adminUid = 'admin_agence_${DateTime.now().millisecondsSinceEpoch}_${email.hashCode.abs()}';
      debugPrint('[ADMIN_CREATION] 🆔 UID Admin Agence généré: $adminUid');

      // 4. Créer le document Firestore
      final userData = {
        'email': email,
        'nom': nom,
        'prenom': prenom,
        'role': 'admin_agence',
        'status': 'actif',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'phone': phone,
        'address': address,
        'temporaryPassword': password, // Stocké temporairement pour l'envoi par email
        'passwordChangeRequired': true,
        'accountType': 'admin_system', // Compte admin système (pas Firebase Auth)
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'super_admin',
        'created_by_uid': currentUser?.uid ?? 'system_super_admin',
        'created_by_email': currentUser?.email ?? '<EMAIL>',
        'isFakeData': false,
        'isLegitimate': true,
        'accessLevel': 'production',
      };

      await _firestore
          .collection('users')
          .doc(adminUid)
          .set(userData);

      debugPrint('[ADMIN_CREATION] ✅ Admin Agence légitime créé avec succès');

      return {
        'success': true,
        'uid': adminUid,
        'email': email,
        'agenceId': agenceId,
        'compagnieId': compagnieId,
      };

    } catch (e) {
      debugPrint('[ADMIN_CREATION] ❌ Erreur création Admin Agence: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
      };
    }
  }

  /// ✅ Approuver une demande professionnelle et créer un compte légitime
  static Future<Map<String, dynamic>> approveProfessionalRequest({
    required String requestId,
    required String compagnieId,
    String? agenceId,
  }) async {
    try {
      debugPrint('[ADMIN_CREATION] ✅ Approbation demande professionnelle: $requestId');

      // 1. Vérifier autorisation Super Admin
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'success': false,
          'error': 'Aucun utilisateur connecté',
        };
      }

      final currentUserDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!currentUserDoc.exists || currentUserDoc.data()!['role'] != 'super_admin') {
        return {
          'success': false,
          'error': 'Seul le Super Admin peut approuver les demandes',
        };
      }

      // 2. Récupérer la demande
      final requestDoc = await _firestore
          .collection('demandes_professionnels')
          .doc(requestId)
          .get();

      if (!requestDoc.exists) {
        return {
          'success': false,
          'error': 'Demande non trouvée',
        };
      }

      final requestData = requestDoc.data()!;
      
      // 3. Créer le compte Firebase Auth
      final email = requestData['email'] as String;
      final password = requestData['password'] as String? ?? 'TempPassword123!';

      UserCredential userCredential;
      try {
        userCredential = await _auth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        );
      } catch (authError) {
        return {
          'success': false,
          'error': 'Erreur création compte: ${authError.toString()}',
        };
      }

      final newUser = userCredential.user!;

      // 4. Créer le document utilisateur légitime
      final userData = {
        'email': email,
        'nom': requestData['nom'],
        'prenom': requestData['prenom'],
        'role': requestData['role'],
        'status': 'actif',
        'compagnieId': compagnieId,
        'agenceId': agenceId,
        'phone': requestData['phone'],
        'address': requestData['address'],
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'professional_request_approval',
        'created_by_uid': currentUser.uid,
        'created_by_email': currentUser.email,
        'approved_request_id': requestId,
        'isFakeData': false,
        'isLegitimate': true,
        'accessLevel': 'production',
      };

      await _firestore
          .collection('users')
          .doc(newUser.uid)
          .set(userData);

      // 5. Marquer la demande comme approuvée
      await requestDoc.reference.update({
        'status': 'approuvee',
        'approved_at': FieldValue.serverTimestamp(),
        'approved_by': currentUser.uid,
        'user_uid': newUser.uid,
      });

      debugPrint('[ADMIN_CREATION] ✅ Demande approuvée et compte créé');

      return {
        'success': true,
        'uid': newUser.uid,
        'email': email,
        'requestId': requestId,
      };

    } catch (e) {
      debugPrint('[ADMIN_CREATION] ❌ Erreur approbation: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
      };
    }
  }
}
